;METADATA=(Diff=true, UseCommands=true)
[/Script/UnrealEd.EditorPerProjectUserSettings]
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=H:/P4/dev/Baoli/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bConfirmEditorClose=False
bSCSEditorShowFloor=False
bAlwaysBuildUAT=True
SCSViewportCameraSpeed=4
bShowSelectionSubcomponents=True
AssetViewerProfileName=
PreviewFeatureLevel=4
PreviewPlatformName=None
PreviewShaderFormatName=None
PreviewShaderPlatformName=None
bPreviewFeatureLevelActive=False
bPreviewFeatureLevelWasDefault=True
PreviewDeviceProfileName=None

[/Script/UnrealEd.EditorStyleSettings]
ApplicationScale=1.000000
bColorVisionDeficiencyCorrection=False
bColorVisionDeficiencyCorrectionPreviewWithDeficiency=False
SelectionColor=(R=0.828000,G=0.364000,B=0.003000,A=1.000000)
AdditionalSelectionColors[0]=(R=0.019382,G=0.496933,B=1.000000,A=1.000000)
AdditionalSelectionColors[1]=(R=0.356400,G=0.040915,B=0.520996,A=1.000000)
AdditionalSelectionColors[2]=(R=1.000000,G=0.168269,B=0.332452,A=1.000000)
AdditionalSelectionColors[3]=(R=1.000000,G=0.051269,B=0.051269,A=1.000000)
AdditionalSelectionColors[4]=(R=1.000000,G=0.715693,B=0.010330,A=1.000000)
AdditionalSelectionColors[5]=(R=0.258183,G=0.539479,B=0.068478,A=1.000000)
ViewportToolOverlayColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
bEnableEditorWindowBackgroundColor=False
EditorWindowBackgroundColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
MenuSearchFieldVisibilityThreshold=10
bUseGrid=True
RegularColor=(R=0.024000,G=0.024000,B=0.024000,A=1.000000)
RuleColor=(R=0.010000,G=0.010000,B=0.010000,A=1.000000)
CenterColor=(R=0.005000,G=0.005000,B=0.005000,A=1.000000)
GridSnapSize=16
GraphBackgroundBrush=(TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),ResourceObject=None,OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=0.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False),bIsDynamicallyLoaded=False,ResourceName="")
bShowNativeComponentNames=True
AssetEditorOpenLocation=Default
bEnableColorizedEditorTabs=True
CurrentAppliedTheme=134380265FBB4A9CA00A1DC9770217B8

[/Script/UnrealEd.EditorLoadingSavingSettings]
AutoReimportDirectorySettings=(SourceDirectory="/Game/",MountPoint="",Wildcards=((Wildcard="Localization/*")))
AutoSaveMaxBackups=10
AutoSaveMethod=BackupAndRestore
bForceCompilationAtStartup=False
RestoreOpenAssetTabsOnRestart=AlwaysPrompt
bMonitorContentDirectories=True
AutoReimportThreshold=3.000000
bAutoCreateAssets=True
bAutoDeleteAssets=True
bDetectChangesOnStartup=True
bPromptBeforeAutoImporting=True
bDeleteSourceFilesWithAssets=False
bAutomaticallyCheckoutOnAssetModification=False
bSCCUseGlobalSettings=False

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
bShouldMinimizeEditorOnNonVRPIE=False
bEmulateStereo=False
SoloAudioInFirstPIEClient=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
bPromoteOutputLogWarningsDuringPIE=False
NewWindowPosition=(X=1280,Y=696)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNumberOfClients=1
PrimaryPIEClientIndex=0
ServerPort=17777
ClientWindowWidth=640
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
bHMDForPrimaryProcessOnly=True
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=Windows@DESKTOP-E41IK6R
LastExecutedLaunchName=DESKTOP-E41IK6R
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)
MultipleInstancePositions=(X=1280,Y=696)

[/Script/UnrealEd.LevelEditorViewportSettings]
CurrentPosGridSize=1
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
bShowActorEditorContext=True
bAllowEditWidgetAxisDisplay=True
bUseLegacyCameraMovementNotifications=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=True
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
TransformWidgetSizeAdjustment=0
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=0.500000
LastInViewportMenuLocation=(X=0.000000,Y=0.000000)
MaterialForDroppedTextures=None
MaterialParamsForDroppedTextures=()
EditorViews=(("/Game/Levels/DefaultLevel.DefaultLevel", (LevelViewportsInfo=((CamPosition=(X=1418.491376,Y=253.652309,Z=113.642834),CamOrthoZoom=9999999562023526247432192.000000),(CamPosition=(X=1418.491376,Y=253.652309,Z=113.642834),CamOrthoZoom=9999999562023526247432192.000000),(CamPosition=(X=1418.491376,Y=253.652309,Z=113.642834),CamOrthoZoom=9999999562023526247432192.000000),(CamPosition=(X=-9127.291088,Y=-3418.861068,Z=155.297867),CamRotation=(Pitch=-8.585792,Yaw=151.683202,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True)))),("/Game/Levels/HouseLevel.HouseLevel", (LevelViewportsInfo=((CamPosition=(X=-7700.397132,Y=104.070842,Z=1289.175792),CamOrthoZoom=303.890411),(CamPosition=(X=-7700.397132,Y=104.070842,Z=1289.175792),CamOrthoZoom=303.890411),(CamPosition=(X=-7700.397132,Y=104.070842,Z=1289.175792),CamOrthoZoom=303.890411),(CamPosition=(X=-5808.065681,Y=174.452962,Z=1295.684329),CamRotation=(Pitch=3.685711,Yaw=-170.939658,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True)))))
PropertyColorationColorForMatchingObjects=(B=0,G=0,R=255,A=255)
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=False,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))

[MRU]
MRUItem0=/Game/Levels/HouseLevel

[/Script/UnrealEd.PersonaOptions]
bAutoAlignFloorToMesh=True
bAlwaysOpenAnimationAssetsInNewTab=False
bMuteAudio=False
DefaultLocalAxesSelection=2
DefaultBoneDrawSelection=1
bShowBoneColors=False
DefaultBoneColor=(R=0.000000,G=0.000000,B=0.025000,A=1.000000)
SelectedBoneColor=(R=0.200000,G=1.000000,B=0.200000,A=1.000000)
AffectedBoneColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
DisabledBoneColor=(R=0.400000,G=0.400000,B=0.400000,A=1.000000)
ParentOfSelectedBoneColor=(R=0.850000,G=0.450000,B=0.120000,A=1.000000)
VirtualBoneColor=(R=0.400000,G=0.400000,B=1.000000,A=1.000000)
SectionTimingNodeColor=(R=0.390000,G=0.390000,B=1.000000,A=0.750000)
NotifyTimingNodeColor=(R=0.800000,G=0.100000,B=0.100000,A=1.000000)
BranchingPointTimingNodeColor=(R=0.500000,G=1.000000,B=1.000000,A=1.000000)
bPauseAnimationOnCameraMove=False
bUseInlineSocketEditor=False
bFlattenSkeletonHierarchyWhenFiltering=False
bHideParentsWhenFiltering=False
bShowBoneIndexes=False
bExpandTreeOnSelection=True
bAllowPreviewMeshCollectionsToSelectFromDifferentSkeletons=True
bAllowPreviewMeshCollectionsToUseCustomAnimBP=False
bAllowMeshSectionSelection=False
NumFolderFiltersInAssetBrowser=2
AssetEditorOptions=(Context="SkeletonEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="SkeletalMeshEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="AnimationEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="AnimationBlueprintEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="PhysicsAssetEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="ControlRigEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
CurveEditorSnapInterval=0.010000
TimelineScrubSnapValue=1000
TimelineDisplayFormat=Frames
bTimelineDisplayPercentage=True
bTimelineDisplayFormatSecondary=True
bTimelineDisplayCurveKeys=False
TimelineEnabledSnaps=CompositeSegment
TimelineEnabledSnaps=MontageSection
bAllowIncompatibleSkeletonSelection=False
bUseTreeViewForAnimationCurves=False
AnimationCurveGroupingDelimiters="._/|\\"

[DetailCustomWidgetExpansion]
BP_BaoliCharacter_C=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
SkeletalMeshComponent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
SpringArmComponent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features
K2Node_VariableGet=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
ControlRigWrapperObject_FRigBoneElement=ControlRigWrapperObject_FRigBoneElement.Transform.Current
BlendSpace=BlendSpace.Axis Settings.Horizontal Axis,BlendSpace.Axis Settings.Vertical Axis,BlendSpace.Analysis.AnalysisPropertiesCombo,BlendSpace.BlendSamples.BlendSamples_Samples
ABP_SandboxCharacter_C=
InputMappingContext=InputMappingContext.Mappings.ActionMappings
InputAction=
K2Node_VariableSet=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
Blueprint=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features
PlayerController=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
PlayerCameraManager=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
ControlRigWrapperObject_FRigControlElement=ControlRigWrapperObject_FRigControlElement.Transform.Current,ControlRigWrapperObject_FRigControlElement.Transform.Initial,ControlRigWrapperObject_FRigControlElement.Transform.Offset,ControlRigWrapperObject_FRigControlElement.Shape.Shape Transform,ControlRigWrapperObject_FRigControlElement.Shape.Shape Properties
PropertyWrapper=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
BaoliPlayerCameraManager_C=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
K2Node_FunctionEntry=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features
K2Node=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
CR_HeadManager_C=
CapsuleComponent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
SkinnedMeshComponent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform
SkeletalMesh=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform
EdGraphNode_Comment=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform
K2Node_Timeline=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
EdGraph=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
MovementComponent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform
K2Node_CallFunction=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
K2Node_CustomEvent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
BoneProxy=BoneProxy.Transforms.Reference,BoneProxy.Transforms.Mesh Relative,BoneProxy.Transforms.Bone
CameraComponent=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform,CameraComponent.PostProcess.Rendering Features,CameraComponent.PostProcess.Rendering Features
LODInfoUILayout=
MeshNaniteSettings=
CR_AO_C=
AnimGraphNode_BlendSpaceGraph=BlendSpace.Axis Settings.Horizontal Axis,BlendSpace.Axis Settings.Vertical Axis,BlendSpace.BlendSamples.BlendSamples_Samples,AnimGraphNode_BlendSpaceGraph.Blend Space.None
BlendSpaceGraph=BlendSpace.Axis Settings.Horizontal Axis,BlendSpace.Axis Settings.Vertical Axis,BlendSpace.BlendSamples.BlendSamples_Samples,AnimGraphNode_BlendSpaceGraph.Blend Space.None
AnimGraphNode_BlendSpacePlayer=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
EulerAnalysisProperties=BlendSpace.Axis Settings.Horizontal Axis,BlendSpace.Axis Settings.Vertical Axis,BlendSpace.Analysis.AnalysisPropertiesCombo,BlendSpace.BlendSamples.BlendSamples_Samples
AnimGraphNode_ApplyMeshSpaceAdditive=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
K2Node_MakeStruct=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
K2Node_SetFieldsInStruct=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
AnimGraphNode_ControlRig=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
Get_AOValue=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform
Get_AOValue_16=BP_BaoliCharacter_C.TransformCommon.Transform,BP_BaoliCharacter_C.Physics.ConstraintsGroup,BP_BaoliCharacter_C.Baoli_Character.Rendering Features,K2Node_VariableGet.DefaultValueCategory.RootTransform,K2Node_VariableSet.DefaultValueCategory.RootTransform

[EditorStartup]
LastLevel=/Game/Levels/DefaultLevel

[ModuleFileTracking]
Baoli.TimeStamp=2025.06.11-10.40.49
Baoli.LastCompileMethod=External
StorageServerClient.TimeStamp=2025.03.24-15.02.14
StorageServerClient.LastCompileMethod=Unknown
CookOnTheFly.TimeStamp=2025.03.24-15.01.58
CookOnTheFly.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.03.24-15.02.14
StreamingFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.03.24-15.02.07
NetworkFile.LastCompileMethod=Unknown
PakFile.TimeStamp=2025.03.24-15.02.07
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.03.24-15.02.11
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.03.24-15.02.11
SandboxFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.03.24-15.01.58
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.03.24-15.02.03
Engine.LastCompileMethod=Unknown
UniversalObjectLocator.TimeStamp=2025.03.24-15.02.15
UniversalObjectLocator.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.03.24-15.02.11
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.03.24-15.01.55
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.03.24-15.02.11
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.03.24-15.02.05
Landscape.LastCompileMethod=Unknown
RHICore.TimeStamp=2025.03.24-15.02.11
RHICore.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.03.24-15.02.11
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.03.24-15.02.14
TextureCompressor.LastCompileMethod=Unknown
OpenColorIOWrapper.TimeStamp=2025.03.24-15.02.07
OpenColorIOWrapper.LastCompileMethod=Unknown
Virtualization.TimeStamp=2025.03.24-15.02.16
Virtualization.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.03.24-15.02.07
MessageLog.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.03.24-15.01.56
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.03.24-15.02.11
PropertyEditor.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.03.24-15.01.55
AnimationModifiers.LastCompileMethod=Unknown
IoStoreOnDemand.TimeStamp=2025.03.24-15.02.05
IoStoreOnDemand.LastCompileMethod=Unknown
OpusAudioDecoder.TimeStamp=2025.03.24-15.02.07
OpusAudioDecoder.LastCompileMethod=Unknown
VorbisAudioDecoder.TimeStamp=2025.03.24-15.02.16
VorbisAudioDecoder.LastCompileMethod=Unknown
AdpcmAudioDecoder.TimeStamp=2025.03.24-15.01.55
AdpcmAudioDecoder.LastCompileMethod=Unknown
BinkAudioDecoder.TimeStamp=2025.03.24-15.01.57
BinkAudioDecoder.LastCompileMethod=Unknown
RadAudioDecoder.TimeStamp=2025.03.24-15.02.11
RadAudioDecoder.LastCompileMethod=Unknown
FastBuildController.TimeStamp=2025.03.24-15.07.42
FastBuildController.LastCompileMethod=Unknown
UbaController.TimeStamp=2025.03.24-15.08.31
UbaController.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.03.24-15.08.41
XGEController.LastCompileMethod=Unknown
PerforceSourceControl.TimeStamp=2025.03.24-15.06.55
PerforceSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.03.24-15.02.11
SourceControl.LastCompileMethod=Unknown
PlasticSourceControl.TimeStamp=2025.03.24-15.06.57
PlasticSourceControl.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.03.24-15.07.32
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.03.24-15.07.32
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoOpenSSL.TimeStamp=2025.03.24-15.07.32
PlatformCryptoOpenSSL.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.03.24-15.07.32
PythonScriptPluginPreload.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.03.24-15.01.59
DesktopPlatform.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.03.24-15.06.55
ChaosCloth.LastCompileMethod=Unknown
PCGCompute.TimeStamp=2025.03.24-15.08.05
PCGCompute.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.03.24-15.07.42
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.03.24-15.07.42
NiagaraVertexFactories.LastCompileMethod=Unknown
ExrReaderGpu.TimeStamp=2025.03.24-15.07.51
ExrReaderGpu.LastCompileMethod=Unknown
WmfMedia.TimeStamp=2025.03.24-15.07.54
WmfMedia.LastCompileMethod=Unknown
Media.TimeStamp=2025.03.24-15.02.06
Media.LastCompileMethod=Unknown
OnlineServicesInterface.TimeStamp=2025.03.24-15.07.56
OnlineServicesInterface.LastCompileMethod=Unknown
OnlineServicesCommon.TimeStamp=2025.03.24-15.07.56
OnlineServicesCommon.LastCompileMethod=Unknown
OnlineServicesCommonEngineUtils.TimeStamp=2025.03.24-15.07.56
OnlineServicesCommonEngineUtils.LastCompileMethod=Unknown
EOSShared.TimeStamp=2025.03.24-15.07.56
EOSShared.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.03.24-15.08.03
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.03.24-15.02.03
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.03.24-15.02.11
SSL.LastCompileMethod=Unknown
XMPP.TimeStamp=2025.03.24-15.02.18
XMPP.LastCompileMethod=Unknown
WebSockets.TimeStamp=2025.03.24-15.02.16
WebSockets.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.03.24-15.08.05
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.03.24-15.02.11
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.03.24-15.08.05
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.03.24-15.08.05
OnlineBlueprintSupport.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.03.24-15.08.09
ChunkDownloader.LastCompileMethod=Unknown
ComputeFramework.TimeStamp=2025.03.24-15.08.13
ComputeFramework.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=2025.03.24-15.08.13
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
HairStrandsCore.TimeStamp=2025.03.24-15.08.17
HairStrandsCore.LastCompileMethod=Unknown
WindowsDeviceProfileSelector.TimeStamp=2025.03.24-15.08.30
WindowsDeviceProfileSelector.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.03.24-15.06.41
AISupportModule.LastCompileMethod=Unknown
ACLPlugin.TimeStamp=2025.03.24-15.06.41
ACLPlugin.LastCompileMethod=Unknown
OptimusSettings.TimeStamp=2025.03.24-15.06.52
OptimusSettings.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.03.24-15.06.57
PixWinPlugin.LastCompileMethod=Unknown
RenderDocPlugin.TimeStamp=2025.03.24-15.06.57
RenderDocPlugin.LastCompileMethod=Unknown
DatasmithContent.TimeStamp=2025.03.24-15.07.10
DatasmithContent.LastCompileMethod=Unknown
GLTFExporter.TimeStamp=2025.03.24-15.07.10
GLTFExporter.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.03.24-15.07.11
VariantManagerContent.LastCompileMethod=Unknown
EditorPerformance.TimeStamp=2025.03.24-15.07.20
EditorPerformance.LastCompileMethod=Unknown
EditorTelemetry.TimeStamp=2025.03.24-15.07.20
EditorTelemetry.LastCompileMethod=Unknown
GPULightmass.TimeStamp=2025.03.24-15.07.20
GPULightmass.LastCompileMethod=Unknown
NFORDenoise.TimeStamp=2025.03.24-15.07.23
NFORDenoise.LastCompileMethod=Unknown
AnalyticsLog.TimeStamp=2025.03.24-15.07.32
AnalyticsLog.LastCompileMethod=Unknown
AnalyticsHorde.TimeStamp=2025.03.24-15.07.32
AnalyticsHorde.LastCompileMethod=Unknown
StudioTelemetry.TimeStamp=2025.03.24-15.07.32
StudioTelemetry.LastCompileMethod=Unknown
Analytics.TimeStamp=2025.03.24-15.01.55
Analytics.LastCompileMethod=Unknown
TelemetryUtils.TimeStamp=2025.03.24-15.02.14
TelemetryUtils.LastCompileMethod=Unknown
NNEDenoiserShaders.TimeStamp=2025.03.24-15.07.54
NNEDenoiserShaders.LastCompileMethod=Unknown
LauncherChunkInstaller.TimeStamp=2025.03.24-15.08.08
LauncherChunkInstaller.LastCompileMethod=Unknown
HoldoutComposite.TimeStamp=2025.03.24-15.07.13
HoldoutComposite.LastCompileMethod=Unknown
D3D12RHI.TimeStamp=2025.03.24-15.01.58
D3D12RHI.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.03.24-15.02.18
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.03.24-15.02.03
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.03.24-15.01.56
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.03.24-15.01.58
Chaos.LastCompileMethod=Unknown
GeometryCore.TimeStamp=2025.03.24-15.02.03
GeometryCore.LastCompileMethod=Unknown
ChaosSolverEngine.TimeStamp=2025.03.24-15.01.58
ChaosSolverEngine.LastCompileMethod=Unknown
ChaosVDRuntime.TimeStamp=2025.03.24-15.01.58
ChaosVDRuntime.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.03.24-15.01.59
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.03.24-15.02.11
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.03.24-15.02.05
InputCore.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.03.24-15.02.14
TargetPlatform.LastCompileMethod=Unknown
TurnkeySupport.TimeStamp=2025.03.24-15.02.14
TurnkeySupport.LastCompileMethod=Unknown
TextureFormat.TimeStamp=2025.03.24-15.02.14
TextureFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.03.24-15.02.14
TextureFormatASTC.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.03.24-15.02.14
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.03.24-15.02.14
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.03.24-15.02.14
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.03.24-15.02.14
TextureFormatUncompressed.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.03.24-15.06.57
TextureFormatOodle.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.03.24-15.02.05
ImageWrapper.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.03.24-15.01.37
AndroidTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatformSettings.TimeStamp=2025.03.24-15.01.37
AndroidTargetPlatformSettings.LastCompileMethod=Unknown
AndroidTargetPlatformControls.TimeStamp=2025.03.24-15.01.37
AndroidTargetPlatformControls.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.03.24-15.01.45
IOSTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatformSettings.TimeStamp=2025.03.24-15.01.45
IOSTargetPlatformSettings.LastCompileMethod=Unknown
IOSTargetPlatformControls.TimeStamp=2025.03.24-15.01.45
IOSTargetPlatformControls.LastCompileMethod=Unknown
LinuxTargetPlatform.TimeStamp=2025.03.24-15.01.46
LinuxTargetPlatform.LastCompileMethod=Unknown
LinuxTargetPlatformSettings.TimeStamp=2025.03.24-15.01.46
LinuxTargetPlatformSettings.LastCompileMethod=Unknown
LinuxTargetPlatformControls.TimeStamp=2025.03.24-15.01.46
LinuxTargetPlatformControls.LastCompileMethod=Unknown
LinuxArm64TargetPlatform.TimeStamp=2025.03.24-15.01.46
LinuxArm64TargetPlatform.LastCompileMethod=Unknown
LinuxArm64TargetPlatformSettings.TimeStamp=2025.03.24-15.01.46
LinuxArm64TargetPlatformSettings.LastCompileMethod=Unknown
LinuxArm64TargetPlatformControls.TimeStamp=2025.03.24-15.01.46
LinuxArm64TargetPlatformControls.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.03.24-15.02.06
MacTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatformSettings.TimeStamp=2025.03.24-15.02.06
MacTargetPlatformSettings.LastCompileMethod=Unknown
MacTargetPlatformControls.TimeStamp=2025.03.24-15.02.06
MacTargetPlatformControls.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.03.24-15.01.45
TVOSTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatformSettings.TimeStamp=2025.03.24-15.01.45
TVOSTargetPlatformSettings.LastCompileMethod=Unknown
TVOSTargetPlatformControls.TimeStamp=2025.03.24-15.01.45
TVOSTargetPlatformControls.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.03.24-15.02.18
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatformSettings.TimeStamp=2025.03.24-15.02.18
WindowsTargetPlatformSettings.LastCompileMethod=Unknown
WindowsTargetPlatformControls.TimeStamp=2025.03.24-15.02.18
WindowsTargetPlatformControls.LastCompileMethod=Unknown
AudioFormatOPUS.TimeStamp=2025.03.24-15.01.56
AudioFormatOPUS.LastCompileMethod=Unknown
AudioFormatOGG.TimeStamp=2025.03.24-15.01.56
AudioFormatOGG.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.03.24-15.01.56
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatBINK.TimeStamp=2025.03.24-15.01.56
AudioFormatBINK.LastCompileMethod=Unknown
AudioFormatRAD.TimeStamp=2025.03.24-15.01.56
AudioFormatRAD.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.03.24-15.02.11
ShaderFormatVectorVM.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.03.24-15.02.11
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.03.24-15.02.11
ShaderFormatOpenGL.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.03.24-15.02.16
VulkanShaderFormat.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.03.24-15.02.07
MetalShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.03.24-15.01.59
DerivedDataCache.LastCompileMethod=Unknown
ShaderPreprocessor.TimeStamp=2025.03.24-15.02.11
ShaderPreprocessor.LastCompileMethod=Unknown
NullInstallBundleManager.TimeStamp=2025.03.24-15.02.07
NullInstallBundleManager.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.03.24-15.01.56
AssetRegistry.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.03.24-15.02.14
TargetDeviceServices.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.03.24-15.02.07
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.03.24-15.02.06
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.03.24-15.02.07
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.03.24-15.02.07
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.03.24-15.02.11
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.03.24-15.06.59
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.03.24-15.07.32
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.03.24-15.02.07
MeshBoneReduction.LastCompileMethod=Unknown
StaticMeshDescription.TimeStamp=2025.03.24-15.02.11
StaticMeshDescription.LastCompileMethod=Unknown
GeometryProcessingInterfaces.TimeStamp=2025.03.24-15.02.03
GeometryProcessingInterfaces.LastCompileMethod=Unknown
NaniteBuilder.TimeStamp=2025.03.24-15.02.07
NaniteBuilder.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.03.24-15.02.07
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.03.24-15.02.05
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.03.24-15.02.07
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.03.24-15.02.11
Sequencer.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.03.24-15.01.58
CurveEditor.LastCompileMethod=Unknown
AssetDefinition.TimeStamp=2025.03.24-15.01.56
AssetDefinition.LastCompileMethod=Unknown
Core.TimeStamp=2025.03.24-15.01.58
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.03.24-15.02.07
Networking.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.03.24-15.02.05
LiveCoding.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.03.24-15.02.03
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.03.24-15.02.11
SourceCodeAccess.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.03.24-15.02.07
Messaging.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.03.24-15.02.07
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.03.24-15.02.16
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.03.24-15.02.05
LandscapeEditorUtilities.LastCompileMethod=Unknown
SubobjectDataInterface.TimeStamp=2025.03.24-15.02.14
SubobjectDataInterface.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.03.24-15.02.11
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.03.24-15.02.11
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.03.24-15.02.11
SlateReflector.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.03.24-15.01.59
EditorStyle.LastCompileMethod=Unknown
UMG.TimeStamp=2025.03.24-15.02.14
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.03.24-15.02.15
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.03.24-15.01.56
AssetTools.LastCompileMethod=Unknown
ScriptableEditorWidgets.TimeStamp=2025.03.24-15.02.11
ScriptableEditorWidgets.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.03.24-15.01.58
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.03.24-15.02.18
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.03.24-15.02.03
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.03.24-15.01.56
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.03.24-15.02.03
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.03.24-15.02.14
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.03.24-15.02.16
VREditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.03.24-15.02.07
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.03.24-15.02.07
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.03.24-15.02.06
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.03.24-15.01.58
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.03.24-15.01.58
ClothingSystemEditor.LastCompileMethod=Unknown
AnimationDataController.TimeStamp=2025.03.24-15.01.55
AnimationDataController.LastCompileMethod=Unknown
TimeManagement.TimeStamp=2025.03.24-15.02.14
TimeManagement.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.03.24-15.01.55
AnimGraph.LastCompileMethod=Unknown
WorldPartitionEditor.TimeStamp=2025.03.24-15.02.18
WorldPartitionEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.03.24-15.02.07
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.03.24-15.02.07
NetworkReplayStreaming.LastCompileMethod=Unknown
MassEntity.TimeStamp=2025.03.24-15.02.06
MassEntity.LastCompileMethod=Unknown
MassEntityTestSuite.TimeStamp=2025.03.24-15.02.06
MassEntityTestSuite.LastCompileMethod=Unknown
AndroidFileServer.TimeStamp=2025.03.24-15.08.08
AndroidFileServer.LastCompileMethod=Unknown
WebMMoviePlayer.TimeStamp=2025.03.24-15.08.30
WebMMoviePlayer.LastCompileMethod=Unknown
WindowsMoviePlayer.TimeStamp=2025.03.24-15.08.30
WindowsMoviePlayer.LastCompileMethod=Unknown
PlatformFunctions.TimeStamp=2025.05.28-11.35.05
PlatformFunctions.LastCompileMethod=Unknown
ChooserUncooked.TimeStamp=2025.03.24-15.06.55
ChooserUncooked.LastCompileMethod=Unknown
EnhancedInput.TimeStamp=2025.03.24-15.06.59
EnhancedInput.LastCompileMethod=Unknown
InputBlueprintNodes.TimeStamp=2025.03.24-15.06.59
InputBlueprintNodes.LastCompileMethod=Unknown
BlueprintGraph.TimeStamp=2025.03.24-15.01.57
BlueprintGraph.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.03.24-15.07.42
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.03.24-15.07.42
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.03.24-15.07.42
NiagaraEditor.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.03.24-15.01.58
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.03.24-15.01.58
ContentBrowserData.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.03.24-15.02.14
ToolMenus.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.03.24-15.02.05
LevelSequence.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.03.24-15.02.11
SignalProcessing.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.03.24-15.07.42
NiagaraAnimNotifies.LastCompileMethod=Unknown
NiagaraSimCaching.TimeStamp=2025.03.24-15.07.46
NiagaraSimCaching.LastCompileMethod=Unknown
NiagaraSimCachingEditor.TimeStamp=2025.03.24-15.07.46
NiagaraSimCachingEditor.LastCompileMethod=Unknown
InterchangeNodes.TimeStamp=2025.03.24-15.07.47
InterchangeNodes.LastCompileMethod=Unknown
InterchangeFactoryNodes.TimeStamp=2025.03.24-15.07.47
InterchangeFactoryNodes.LastCompileMethod=Unknown
InterchangeImport.TimeStamp=2025.03.24-15.07.47
InterchangeImport.LastCompileMethod=Unknown
InterchangePipelines.TimeStamp=2025.03.24-15.07.47
InterchangePipelines.LastCompileMethod=Unknown
ImgMediaEngine.TimeStamp=2025.03.24-15.07.51
ImgMediaEngine.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.03.24-15.07.54
ActorSequence.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.03.24-15.08.09
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.03.24-15.08.09
AudioSynesthesia.LastCompileMethod=Unknown
AudioAnalyzer.TimeStamp=2025.03.24-15.01.56
AudioAnalyzer.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.03.24-15.08.09
CableComponent.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.03.24-15.08.13
CustomMeshComponent.LastCompileMethod=Unknown
DataRegistry.TimeStamp=2025.03.24-15.08.13
DataRegistry.LastCompileMethod=Unknown
DataRegistryEditor.TimeStamp=2025.03.24-15.08.13
DataRegistryEditor.LastCompileMethod=Unknown
GameFeatures.TimeStamp=2025.03.24-15.08.13
GameFeatures.LastCompileMethod=Unknown
GeometryScriptingCore.TimeStamp=2025.03.24-15.08.14
GeometryScriptingCore.LastCompileMethod=Unknown
LocationServicesBPLibrary.TimeStamp=2025.03.24-15.08.19
LocationServicesBPLibrary.LastCompileMethod=Unknown
MetasoundGraphCore.TimeStamp=2025.03.24-15.08.20
MetasoundGraphCore.LastCompileMethod=Unknown
MetasoundGenerator.TimeStamp=2025.03.24-15.08.20
MetasoundGenerator.LastCompileMethod=Unknown
MetasoundFrontend.TimeStamp=2025.03.24-15.08.20
MetasoundFrontend.LastCompileMethod=Unknown
MetasoundStandardNodes.TimeStamp=2025.03.24-15.08.22
MetasoundStandardNodes.LastCompileMethod=Unknown
MetasoundEngine.TimeStamp=2025.03.24-15.08.20
MetasoundEngine.LastCompileMethod=Unknown
WaveTable.TimeStamp=2025.03.24-15.08.30
WaveTable.LastCompileMethod=Unknown
MetasoundEngineTest.TimeStamp=2025.03.24-15.08.20
MetasoundEngineTest.LastCompileMethod=Unknown
MetasoundEditor.TimeStamp=2025.03.24-15.08.20
MetasoundEditor.LastCompileMethod=Unknown
AudioWidgets.TimeStamp=2025.03.24-15.08.09
AudioWidgets.LastCompileMethod=Unknown
AdvancedWidgets.TimeStamp=2025.03.24-15.01.55
AdvancedWidgets.LastCompileMethod=Unknown
ModularGameplay.TimeStamp=2025.03.24-15.08.23
ModularGameplay.LastCompileMethod=Unknown
MsQuicRuntime.TimeStamp=2025.03.24-15.08.23
MsQuicRuntime.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.03.24-15.08.28
ProceduralMeshComponent.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.03.24-15.08.28
PropertyAccessEditor.LastCompileMethod=Unknown
RigVM.TimeStamp=2025.03.24-15.08.29
RigVM.LastCompileMethod=Unknown
RigVMDeveloper.TimeStamp=2025.03.24-15.08.29
RigVMDeveloper.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.03.24-15.08.30
SignificanceManager.LastCompileMethod=Unknown
ScriptableToolsFramework.TimeStamp=2025.03.24-15.08.30
ScriptableToolsFramework.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.03.24-15.08.30
SoundFields.LastCompileMethod=Unknown
ResonanceAudio.TimeStamp=2025.03.24-15.08.29
ResonanceAudio.LastCompileMethod=Unknown
StateTreeModule.TimeStamp=2025.03.24-15.08.30
StateTreeModule.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.03.24-15.02.14
TraceServices.LastCompileMethod=Unknown
TraceAnalysis.TimeStamp=2025.03.24-15.02.14
TraceAnalysis.LastCompileMethod=Unknown
StateTreeTestSuite.TimeStamp=2025.03.24-15.08.30
StateTreeTestSuite.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.03.24-15.08.30
Synthesis.LastCompileMethod=Unknown
WebBrowserWidget.TimeStamp=2025.03.24-15.08.30
WebBrowserWidget.LastCompileMethod=Unknown
WebBrowser.TimeStamp=2025.03.24-15.02.16
WebBrowser.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.03.24-15.06.41
EnvironmentQueryEditor.LastCompileMethod=Unknown
Paper2D.TimeStamp=2025.03.24-15.06.41
Paper2D.LastCompileMethod=Unknown
AnimationData.TimeStamp=2025.03.24-15.06.41
AnimationData.LastCompileMethod=Unknown
AnimationLocomotionLibraryRuntime.TimeStamp=2025.03.24-15.06.42
AnimationLocomotionLibraryRuntime.LastCompileMethod=Unknown
AnimationLocomotionLibraryEditor.TimeStamp=2025.03.24-15.06.42
AnimationLocomotionLibraryEditor.LastCompileMethod=Unknown
BlendStack.TimeStamp=2025.03.24-15.06.42
BlendStack.LastCompileMethod=Unknown
BlendStackEditor.TimeStamp=2025.03.24-15.06.42
BlendStackEditor.LastCompileMethod=Unknown
AnimationWarpingRuntime.TimeStamp=2025.03.24-15.06.42
AnimationWarpingRuntime.LastCompileMethod=Unknown
AnimationWarpingEditor.TimeStamp=2025.03.24-15.06.42
AnimationWarpingEditor.LastCompileMethod=Unknown
ControlRig.TimeStamp=2025.03.24-15.06.42
ControlRig.LastCompileMethod=Unknown
Constraints.TimeStamp=2025.03.24-15.01.58
Constraints.LastCompileMethod=Unknown
ControlRigDeveloper.TimeStamp=2025.03.24-15.06.42
ControlRigDeveloper.LastCompileMethod=Unknown
OptimusCore.TimeStamp=2025.03.24-15.06.52
OptimusCore.LastCompileMethod=Unknown
OptimusDeveloper.TimeStamp=2025.03.24-15.06.52
OptimusDeveloper.LastCompileMethod=Unknown
IKRig.TimeStamp=2025.03.24-15.06.52
IKRig.LastCompileMethod=Unknown
IKRigDeveloper.TimeStamp=2025.03.24-15.06.52
IKRigDeveloper.LastCompileMethod=Unknown
PoseSearch.TimeStamp=2025.03.24-15.06.52
PoseSearch.LastCompileMethod=Unknown
PoseSearchEditor.TimeStamp=2025.03.24-15.06.52
PoseSearchEditor.LastCompileMethod=Unknown
RigLogicLib.TimeStamp=2025.03.24-15.06.52
RigLogicLib.LastCompileMethod=Unknown
RigLogicLibTest.TimeStamp=2025.03.24-15.06.52
RigLogicLibTest.LastCompileMethod=Unknown
RigLogicDeveloper.TimeStamp=2025.03.24-15.06.52
RigLogicDeveloper.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.03.24-15.06.52
GameplayCameras.LastCompileMethod=Unknown
EngineCameras.TimeStamp=2025.03.24-15.06.52
EngineCameras.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.03.24-15.06.55
AnimationSharing.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.03.24-15.06.57
PropertyAccessNode.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.03.24-15.06.57
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.03.24-15.02.14
TreeMap.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.03.24-15.02.05
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.03.24-15.02.06
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.03.24-15.02.03
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.03.24-15.01.58
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.03.24-15.02.08
PixelInspectorModule.LastCompileMethod=Unknown
DataValidation.TimeStamp=2025.03.24-15.06.57
DataValidation.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.03.24-15.06.57
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.03.24-15.06.57
FacialAnimationEditor.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.03.24-15.06.57
GameplayTagsEditor.LastCompileMethod=Unknown
ChaosCaching.TimeStamp=2025.03.24-15.07.13
ChaosCaching.LastCompileMethod=Unknown
ChaosCachingEditor.TimeStamp=2025.03.24-15.07.13
ChaosCachingEditor.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.03.24-15.08.40
TakeRecorder.LastCompileMethod=Unknown
FullBodyIK.TimeStamp=2025.03.24-15.07.20
FullBodyIK.LastCompileMethod=Unknown
PBIK.TimeStamp=2025.03.24-15.07.20
PBIK.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.03.24-15.07.32
PythonScriptPlugin.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.03.24-15.07.54
TcpMessaging.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.03.24-15.07.54
UdpMessaging.LastCompileMethod=Unknown
NNERuntimeORT.TimeStamp=2025.03.24-15.07.56
NNERuntimeORT.LastCompileMethod=Unknown
NNEEditor.TimeStamp=2025.03.24-15.02.07
NNEEditor.LastCompileMethod=Unknown
RD.TimeStamp=2025.05.12-16.49.43
RD.LastCompileMethod=Unknown
RiderLink.TimeStamp=2025.05.12-16.49.57
RiderLink.LastCompileMethod=Unknown
RiderLogging.TimeStamp=2025.05.12-16.50.11
RiderLogging.LastCompileMethod=Unknown
RiderDebuggerSupport.TimeStamp=2025.05.12-16.49.59
RiderDebuggerSupport.LastCompileMethod=Unknown
SQLiteCore.TimeStamp=2025.03.24-15.08.13
SQLiteCore.LastCompileMethod=Unknown
MotionTrajectory.TimeStamp=2025.03.24-15.07.13
MotionTrajectory.LastCompileMethod=Unknown
AnalyticsBlueprintLibrary.TimeStamp=2025.03.24-15.08.08
AnalyticsBlueprintLibrary.LastCompileMethod=Unknown
Concert.TimeStamp=2025.03.24-15.06.55
Concert.LastCompileMethod=Unknown
ConcertClient.TimeStamp=2025.03.24-15.06.55
ConcertClient.LastCompileMethod=Unknown
ConcertTransport.TimeStamp=2025.03.24-15.06.55
ConcertTransport.LastCompileMethod=Unknown
ConcertServer.TimeStamp=2025.03.24-15.06.55
ConcertServer.LastCompileMethod=Unknown
ConcertSyncCore.TimeStamp=2025.03.24-15.06.55
ConcertSyncCore.LastCompileMethod=Unknown
OptimizedWebBrowser.TimeStamp=2025.05.28-11.34.55
OptimizedWebBrowser.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.03.24-15.06.55
ChaosClothEditor.LastCompileMethod=Unknown
ChaosVD.TimeStamp=2025.03.24-15.06.55
ChaosVD.LastCompileMethod=Unknown
ChaosVDBlueprint.TimeStamp=2025.03.24-15.06.55
ChaosVDBlueprint.LastCompileMethod=Unknown
Chooser.TimeStamp=2025.03.24-15.06.55
Chooser.LastCompileMethod=Unknown
ChooserEditor.TimeStamp=2025.03.24-15.06.55
ChooserEditor.LastCompileMethod=Unknown
ProxyTable.TimeStamp=2025.03.24-15.06.55
ProxyTable.LastCompileMethod=Unknown
ProxyTableUncooked.TimeStamp=2025.03.24-15.06.55
ProxyTableUncooked.LastCompileMethod=Unknown
ProxyTableEditor.TimeStamp=2025.03.24-15.06.55
ProxyTableEditor.LastCompileMethod=Unknown
InputEditor.TimeStamp=2025.03.24-15.06.59
InputEditor.LastCompileMethod=Unknown
JsonBlueprintUtilities.TimeStamp=2025.03.24-15.07.47
JsonBlueprintUtilities.LastCompileMethod=Unknown
JsonBlueprintGraph.TimeStamp=2025.03.24-15.07.47
JsonBlueprintGraph.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.03.24-15.07.54
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.03.24-15.07.54
MeshPaintingToolset.LastCompileMethod=Unknown
PCG.TimeStamp=2025.03.24-15.08.05
PCG.LastCompileMethod=Unknown
PCGEditor.TimeStamp=2025.03.24-15.08.05
PCGEditor.LastCompileMethod=Unknown
RenderGraphInsights.TimeStamp=2025.03.24-15.08.08
RenderGraphInsights.LastCompileMethod=Unknown
TraceUtilities.TimeStamp=2025.03.24-15.08.31
TraceUtilities.LastCompileMethod=Unknown
EditorTraceUtilities.TimeStamp=2025.03.24-15.08.31
EditorTraceUtilities.LastCompileMethod=Unknown
TraceTools.TimeStamp=2025.03.24-15.02.14
TraceTools.LastCompileMethod=Unknown
WorldMetricsCore.TimeStamp=2025.03.24-15.08.41
WorldMetricsCore.LastCompileMethod=Unknown
WorldMetricsTest.TimeStamp=2025.03.24-15.08.41
WorldMetricsTest.LastCompileMethod=Unknown
CsvMetrics.TimeStamp=2025.03.24-15.08.41
CsvMetrics.LastCompileMethod=Unknown
NiagaraBlueprintNodes.TimeStamp=2025.03.24-15.07.42
NiagaraBlueprintNodes.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.03.24-15.07.42
NiagaraEditorWidgets.LastCompileMethod=Unknown
NiagaraFluids.TimeStamp=2025.03.24-15.07.46
NiagaraFluids.LastCompileMethod=Unknown
InterchangeEditor.TimeStamp=2025.03.24-15.07.46
InterchangeEditor.LastCompileMethod=Unknown
InterchangeEditorPipelines.TimeStamp=2025.03.24-15.07.47
InterchangeEditorPipelines.LastCompileMethod=Unknown
InterchangeEditorUtilities.TimeStamp=2025.03.24-15.07.47
InterchangeEditorUtilities.LastCompileMethod=Unknown
GLTFCore.TimeStamp=2025.03.24-15.07.47
GLTFCore.LastCompileMethod=Unknown
InterchangeMessages.TimeStamp=2025.03.24-15.07.47
InterchangeMessages.LastCompileMethod=Unknown
InterchangeExport.TimeStamp=2025.03.24-15.07.47
InterchangeExport.LastCompileMethod=Unknown
InterchangeDispatcher.TimeStamp=2025.03.24-15.07.47
InterchangeDispatcher.LastCompileMethod=Unknown
InterchangeCommon.TimeStamp=2025.03.24-15.07.47
InterchangeCommon.LastCompileMethod=Unknown
InterchangeCommonParser.TimeStamp=2025.03.24-15.07.47
InterchangeCommonParser.LastCompileMethod=Unknown
InterchangeFbxParser.TimeStamp=2025.03.24-15.07.47
InterchangeFbxParser.LastCompileMethod=Unknown
ImgMedia.TimeStamp=2025.03.24-15.07.51
ImgMedia.LastCompileMethod=Unknown
MediaCompositing.TimeStamp=2025.03.24-15.07.51
MediaCompositing.LastCompileMethod=Unknown
MediaPlate.TimeStamp=2025.03.24-15.07.51
MediaPlate.LastCompileMethod=Unknown
MediaPlateEditor.TimeStamp=2025.03.24-15.07.51
MediaPlateEditor.LastCompileMethod=Unknown
SequencerScripting.TimeStamp=2025.03.24-15.07.54
SequencerScripting.LastCompileMethod=Unknown
SequencerScriptingEditor.TimeStamp=2025.03.24-15.07.54
SequencerScriptingEditor.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.03.24-15.07.54
TemplateSequence.LastCompileMethod=Unknown
OnlineBase.TimeStamp=2025.03.24-15.07.56
OnlineBase.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.03.24-15.08.08
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.03.24-15.08.08
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
AndroidPermission.TimeStamp=2025.03.24-15.08.08
AndroidPermission.LastCompileMethod=Unknown
AppleImageUtils.TimeStamp=2025.03.24-15.08.08
AppleImageUtils.LastCompileMethod=Unknown
AppleImageUtilsBlueprintSupport.TimeStamp=2025.03.24-15.08.08
AppleImageUtilsBlueprintSupport.LastCompileMethod=Unknown
ArchVisCharacter.TimeStamp=2025.03.24-15.08.08
ArchVisCharacter.LastCompileMethod=Unknown
AudioCapture.TimeStamp=2025.03.24-15.08.08
AudioCapture.LastCompileMethod=Unknown
AudioCaptureWasapi.TimeStamp=2025.03.24-15.01.56
AudioCaptureWasapi.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.03.24-15.08.08
AssetTags.LastCompileMethod=Unknown
AudioWidgetsEditor.TimeStamp=2025.03.24-15.08.09
AudioWidgetsEditor.LastCompileMethod=Unknown
ComputeFrameworkEditor.TimeStamp=2025.03.24-15.08.13
ComputeFrameworkEditor.LastCompileMethod=Unknown
GameFeaturesEditor.TimeStamp=2025.03.24-15.08.13
GameFeaturesEditor.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.03.24-15.08.14
GeometryCacheEd.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.03.24-15.08.14
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.03.24-15.08.14
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.03.24-15.08.14
GeometryCache.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.03.24-15.08.14
GeometryCacheTracks.LastCompileMethod=Unknown
GeometryScriptingEditor.TimeStamp=2025.03.24-15.08.14
GeometryScriptingEditor.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.03.24-15.08.14
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.03.24-15.08.14
DynamicMesh.LastCompileMethod=Unknown
MeshFileUtils.TimeStamp=2025.03.24-15.08.14
MeshFileUtils.LastCompileMethod=Unknown
GooglePAD.TimeStamp=2025.03.24-15.08.17
GooglePAD.LastCompileMethod=Unknown
HairStrandsDeformer.TimeStamp=2025.03.24-15.08.17
HairStrandsDeformer.LastCompileMethod=Unknown
HairStrandsRuntime.TimeStamp=2025.03.24-15.08.17
HairStrandsRuntime.LastCompileMethod=Unknown
HairStrandsEditor.TimeStamp=2025.03.24-15.08.17
HairStrandsEditor.LastCompileMethod=Unknown
HairCardGeneratorFramework.TimeStamp=2025.03.24-15.08.17
HairCardGeneratorFramework.LastCompileMethod=Unknown
InputDebugging.TimeStamp=2025.03.24-15.08.19
InputDebugging.LastCompileMethod=Unknown
InputDebuggingEditor.TimeStamp=2025.03.24-15.08.19
InputDebuggingEditor.LastCompileMethod=Unknown
MeshModelingTools.TimeStamp=2025.03.24-15.08.19
MeshModelingTools.LastCompileMethod=Unknown
MeshModelingToolsEditorOnly.TimeStamp=2025.03.24-15.08.19
MeshModelingToolsEditorOnly.LastCompileMethod=Unknown
ModelingComponents.TimeStamp=2025.03.24-15.08.19
ModelingComponents.LastCompileMethod=Unknown
GeometryFramework.TimeStamp=2025.03.24-15.02.03
GeometryFramework.LastCompileMethod=Unknown
ModelingComponentsEditorOnly.TimeStamp=2025.03.24-15.08.19
ModelingComponentsEditorOnly.LastCompileMethod=Unknown
ModelingOperators.TimeStamp=2025.03.24-15.08.19
ModelingOperators.LastCompileMethod=Unknown
ModelingOperatorsEditorOnly.TimeStamp=2025.03.24-15.08.19
ModelingOperatorsEditorOnly.LastCompileMethod=Unknown
MobilePatchingUtils.TimeStamp=2025.03.24-15.08.23
MobilePatchingUtils.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.03.24-15.08.28
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
EditorScriptableToolsFramework.TimeStamp=2025.03.24-15.08.30
EditorScriptableToolsFramework.LastCompileMethod=Unknown
StateTreeEditorModule.TimeStamp=2025.03.24-15.08.30
StateTreeEditorModule.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.03.24-15.08.30
SynthesisEditor.LastCompileMethod=Unknown
UnrealUSDWrapper.TimeStamp=2025.03.24-15.08.30
UnrealUSDWrapper.LastCompileMethod=Unknown
USDUtilities.TimeStamp=2025.03.24-15.08.30
USDUtilities.LastCompileMethod=Unknown
USDClasses.TimeStamp=2025.03.24-15.08.30
USDClasses.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.03.24-15.08.40
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.03.24-15.08.40
TakeSequencer.LastCompileMethod=Unknown
Paper2DEditor.TimeStamp=2025.03.24-15.06.41
Paper2DEditor.LastCompileMethod=Unknown
PaperSpriteSheetImporter.TimeStamp=2025.03.24-15.06.41
PaperSpriteSheetImporter.LastCompileMethod=Unknown
PaperTiledImporter.TimeStamp=2025.03.24-15.06.41
PaperTiledImporter.LastCompileMethod=Unknown
ACLPluginEditor.TimeStamp=2025.03.24-15.06.41
ACLPluginEditor.LastCompileMethod=Unknown
BlendSpaceMotionAnalysis.TimeStamp=2025.03.24-15.06.42
BlendSpaceMotionAnalysis.LastCompileMethod=Unknown
AnimationModifierLibrary.TimeStamp=2025.03.24-15.06.42
AnimationModifierLibrary.LastCompileMethod=Unknown
GameplayInsights.TimeStamp=2025.03.24-15.06.52
GameplayInsights.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.03.24-15.02.14
TraceInsights.LastCompileMethod=Unknown
TraceInsightsCore.TimeStamp=2025.03.24-15.02.14
TraceInsightsCore.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.03.24-15.01.55
AnimationBlueprintEditor.LastCompileMethod=Unknown
GameplayInsightsEditor.TimeStamp=2025.03.24-15.06.52
GameplayInsightsEditor.LastCompileMethod=Unknown
RewindDebuggerRuntime.TimeStamp=2025.03.24-15.06.52
RewindDebuggerRuntime.LastCompileMethod=Unknown
RewindDebuggerVLogRuntime.TimeStamp=2025.03.24-15.06.52
RewindDebuggerVLogRuntime.LastCompileMethod=Unknown
ControlRigSpline.TimeStamp=2025.03.24-15.06.52
ControlRigSpline.LastCompileMethod=Unknown
LiveLink.TimeStamp=2025.03.24-15.06.52
LiveLink.LastCompileMethod=Unknown
LiveLinkComponents.TimeStamp=2025.03.24-15.06.52
LiveLinkComponents.LastCompileMethod=Unknown
LiveLinkEditor.TimeStamp=2025.03.24-15.06.52
LiveLinkEditor.LastCompileMethod=Unknown
LiveLinkGraphNode.TimeStamp=2025.03.24-15.06.52
LiveLinkGraphNode.LastCompileMethod=Unknown
LiveLinkMovieScene.TimeStamp=2025.03.24-15.06.52
LiveLinkMovieScene.LastCompileMethod=Unknown
LiveLinkSequencer.TimeStamp=2025.03.24-15.06.52
LiveLinkSequencer.LastCompileMethod=Unknown
MotionWarping.TimeStamp=2025.03.24-15.06.52
MotionWarping.LastCompileMethod=Unknown
RigLogicModule.TimeStamp=2025.03.24-15.06.52
RigLogicModule.LastCompileMethod=Unknown
RigLogicEditor.TimeStamp=2025.03.24-15.06.52
RigLogicEditor.LastCompileMethod=Unknown
GameplayCamerasUncookedOnly.TimeStamp=2025.03.24-15.06.55
GameplayCamerasUncookedOnly.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.03.24-15.06.55
AnimationSharingEd.LastCompileMethod=Unknown
CLionSourceCodeAccess.TimeStamp=2025.03.24-15.06.55
CLionSourceCodeAccess.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.03.24-15.06.55
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
DumpGPUServices.TimeStamp=2025.03.24-15.06.55
DumpGPUServices.LastCompileMethod=Unknown
N10XSourceCodeAccess.TimeStamp=2025.03.24-15.06.55
N10XSourceCodeAccess.LastCompileMethod=Unknown
GitSourceControl.TimeStamp=2025.03.24-15.06.55
GitSourceControl.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.03.24-15.06.57
PluginUtils.LastCompileMethod=Unknown
SubversionSourceControl.TimeStamp=2025.03.24-15.06.57
SubversionSourceControl.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.03.24-15.06.57
RiderSourceCodeAccess.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.03.24-15.06.57
UObjectPlugin.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.03.24-15.06.57
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.03.24-15.06.57
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
AssetReferenceRestrictions.TimeStamp=2025.03.24-15.06.57
AssetReferenceRestrictions.LastCompileMethod=Unknown
ChangelistReview.TimeStamp=2025.03.24-15.06.57
ChangelistReview.LastCompileMethod=Unknown
ColorGradingEditor.TimeStamp=2025.03.24-15.06.57
ColorGradingEditor.LastCompileMethod=Unknown
BlueprintHeaderView.TimeStamp=2025.03.24-15.06.57
BlueprintHeaderView.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.03.24-15.06.57
CurveEditorTools.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.03.24-15.06.57
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.03.24-15.06.57
CryptoKeysOpenSSL.LastCompileMethod=Unknown
EditorScriptingUtilities.TimeStamp=2025.03.24-15.06.57
EditorScriptingUtilities.LastCompileMethod=Unknown
EditorDebugTools.TimeStamp=2025.03.24-15.06.57
EditorDebugTools.LastCompileMethod=Unknown
ModelingToolsEditorMode.TimeStamp=2025.03.24-15.06.57
ModelingToolsEditorMode.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.03.24-15.06.57
MaterialAnalyzer.LastCompileMethod=Unknown
MobileLauncherProfileWizard.TimeStamp=2025.03.24-15.06.57
MobileLauncherProfileWizard.LastCompileMethod=Unknown
MeshLODToolset.TimeStamp=2025.03.24-15.06.57
MeshLODToolset.LastCompileMethod=Unknown
SpeedTreeImporter.TimeStamp=2025.03.24-15.06.59
SpeedTreeImporter.LastCompileMethod=Unknown
ScriptableToolsEditorMode.TimeStamp=2025.03.24-15.06.59
ScriptableToolsEditorMode.LastCompileMethod=Unknown
SequencerAnimTools.TimeStamp=2025.03.24-15.06.59
SequencerAnimTools.LastCompileMethod=Unknown
StylusInput.TimeStamp=2025.03.24-15.06.59
StylusInput.LastCompileMethod=Unknown
StylusInputDebugWidget.TimeStamp=2025.03.24-15.06.59
StylusInputDebugWidget.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.03.24-15.06.57
PluginBrowser.LastCompileMethod=Unknown
UMGWidgetPreview.TimeStamp=2025.03.24-15.06.59
UMGWidgetPreview.LastCompileMethod=Unknown
AxFImporter.TimeStamp=2025.03.24-15.06.59
AxFImporter.LastCompileMethod=Unknown
WorldPartitionHLODUtilities.TimeStamp=2025.03.24-15.06.59
WorldPartitionHLODUtilities.LastCompileMethod=Unknown
UVEditor.TimeStamp=2025.03.24-15.06.59
UVEditor.LastCompileMethod=Unknown
UVEditorTools.TimeStamp=2025.03.24-15.06.59
UVEditorTools.LastCompileMethod=Unknown
UVEditorToolsEditorOnly.TimeStamp=2025.03.24-15.06.59
UVEditorToolsEditorOnly.LastCompileMethod=Unknown
DatasmithContentEditor.TimeStamp=2025.03.24-15.07.10
DatasmithContentEditor.LastCompileMethod=Unknown
MDLImporter.TimeStamp=2025.03.24-15.07.11
MDLImporter.LastCompileMethod=Unknown
VariantManager.TimeStamp=2025.03.24-15.07.11
VariantManager.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.03.24-15.07.11
VariantManagerContentEditor.LastCompileMethod=Unknown
AdvancedRenamer.TimeStamp=2025.03.24-15.07.11
AdvancedRenamer.LastCompileMethod=Unknown
ActorPalette.TimeStamp=2025.03.24-15.07.11
ActorPalette.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.03.24-15.07.13
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.03.24-15.07.13
AutomationUtilsEditor.LastCompileMethod=Unknown
BackChannel.TimeStamp=2025.03.24-15.07.13
BackChannel.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.03.24-15.07.13
FractureEditor.LastCompileMethod=Unknown
ChaosUserDataPT.TimeStamp=2025.03.24-15.07.13
ChaosUserDataPT.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.03.24-15.07.13
ChaosNiagara.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.03.24-15.07.13
ChaosSolverEditor.LastCompileMethod=Unknown
DataflowAssetTools.TimeStamp=2025.03.24-15.07.20
DataflowAssetTools.LastCompileMethod=Unknown
DataflowEnginePlugin.TimeStamp=2025.03.24-15.07.20
DataflowEnginePlugin.LastCompileMethod=Unknown
DataflowSimulation.TimeStamp=2025.03.24-15.01.58
DataflowSimulation.LastCompileMethod=Unknown
DataflowNodes.TimeStamp=2025.03.24-15.07.20
DataflowNodes.LastCompileMethod=Unknown
TedsCore.TimeStamp=2025.03.24-15.07.20
TedsCore.LastCompileMethod=Unknown
TypedElementFramework.TimeStamp=2025.03.24-15.02.14
TypedElementFramework.LastCompileMethod=Unknown
MassEntityEditor.TimeStamp=2025.03.24-15.02.06
MassEntityEditor.LastCompileMethod=Unknown
MassEntityDebugger.TimeStamp=2025.03.24-15.02.06
MassEntityDebugger.LastCompileMethod=Unknown
TedsUI.TimeStamp=2025.03.24-15.07.20
TedsUI.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.03.24-15.07.20
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.03.24-15.07.20
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.03.24-15.07.20
GeometryCollectionSequencer.LastCompileMethod=Unknown
GeometryCollectionEngine.TimeStamp=2025.03.24-15.02.03
GeometryCollectionEngine.LastCompileMethod=Unknown
GeometryCollectionNodes.TimeStamp=2025.03.24-15.07.20
GeometryCollectionNodes.LastCompileMethod=Unknown
GeometryCollectionDepNodes.TimeStamp=2025.03.24-15.07.20
GeometryCollectionDepNodes.LastCompileMethod=Unknown
GeometryFlowCore.TimeStamp=2025.03.24-15.07.20
GeometryFlowCore.LastCompileMethod=Unknown
GeometryFlowMeshProcessing.TimeStamp=2025.03.24-15.07.20
GeometryFlowMeshProcessing.LastCompileMethod=Unknown
GeometryFlowMeshProcessingEditor.TimeStamp=2025.03.24-15.07.20
GeometryFlowMeshProcessingEditor.LastCompileMethod=Unknown
GPULightmassEditor.TimeStamp=2025.03.24-15.07.20
GPULightmassEditor.LastCompileMethod=Unknown
LocalizableMessage.TimeStamp=2025.03.24-15.07.21
LocalizableMessage.LastCompileMethod=Unknown
LocalizableMessageBlueprint.TimeStamp=2025.03.24-15.07.21
LocalizableMessageBlueprint.LastCompileMethod=Unknown
MeshModelingToolsExp.TimeStamp=2025.03.24-15.07.21
MeshModelingToolsExp.LastCompileMethod=Unknown
MeshModelingToolsEditorOnlyExp.TimeStamp=2025.03.24-15.07.21
MeshModelingToolsEditorOnlyExp.LastCompileMethod=Unknown
GeometryProcessingAdapters.TimeStamp=2025.03.24-15.07.21
GeometryProcessingAdapters.LastCompileMethod=Unknown
ModelingEditorUI.TimeStamp=2025.03.24-15.07.21
ModelingEditorUI.LastCompileMethod=Unknown
ModelingUI.TimeStamp=2025.03.24-15.07.21
ModelingUI.LastCompileMethod=Unknown
SkeletalMeshModifiers.TimeStamp=2025.03.24-15.07.21
SkeletalMeshModifiers.LastCompileMethod=Unknown
ToolPresetAsset.TimeStamp=2025.03.24-15.07.42
ToolPresetAsset.LastCompileMethod=Unknown
ToolPresetEditor.TimeStamp=2025.03.24-15.07.42
ToolPresetEditor.LastCompileMethod=Unknown
USDSchemas.TimeStamp=2025.03.24-15.07.46
USDSchemas.LastCompileMethod=Unknown
USDStage.TimeStamp=2025.03.24-15.07.46
USDStage.LastCompileMethod=Unknown
UniversalObjectLocatorEditor.TimeStamp=2025.03.24-15.02.15
UniversalObjectLocatorEditor.LastCompileMethod=Unknown
USDStageEditor.TimeStamp=2025.03.24-15.07.46
USDStageEditor.LastCompileMethod=Unknown
USDStageImporter.TimeStamp=2025.03.24-15.07.46
USDStageImporter.LastCompileMethod=Unknown
USDStageEditorViewModels.TimeStamp=2025.03.24-15.07.46
USDStageEditorViewModels.LastCompileMethod=Unknown
USDExporter.TimeStamp=2025.03.24-15.07.46
USDExporter.LastCompileMethod=Unknown
USDClassesEditor.TimeStamp=2025.03.24-15.07.46
USDClassesEditor.LastCompileMethod=Unknown
GeometryCacheUSD.TimeStamp=2025.03.24-15.07.46
GeometryCacheUSD.LastCompileMethod=Unknown
USDTests.TimeStamp=2025.03.24-15.07.46
USDTests.LastCompileMethod=Unknown
AlembicImporter.TimeStamp=2025.03.24-15.07.46
AlembicImporter.LastCompileMethod=Unknown
AlembicLibrary.TimeStamp=2025.03.24-15.07.46
AlembicLibrary.LastCompileMethod=Unknown
NNEDenoiser.TimeStamp=2025.03.24-15.07.54
NNEDenoiser.LastCompileMethod=Unknown
InterchangeTests.TimeStamp=2025.03.24-15.08.31
InterchangeTests.LastCompileMethod=Unknown
InterchangeTestEditor.TimeStamp=2025.03.24-15.08.31
InterchangeTestEditor.LastCompileMethod=Unknown
PCGGeometryScriptInterop.TimeStamp=2025.03.24-15.08.08
PCGGeometryScriptInterop.LastCompileMethod=Unknown
Reflex.TimeStamp=2025.03.24-15.08.26
Reflex.LastCompileMethod=Unknown
MetaHumanSDKEditor.TimeStamp=2025.03.24-15.07.21
MetaHumanSDKEditor.LastCompileMethod=Unknown
MetaHumanSDKRuntime.TimeStamp=2025.03.24-15.07.21
MetaHumanSDKRuntime.LastCompileMethod=Unknown
RiderBlueprint.TimeStamp=2025.05.12-16.50.12
RiderBlueprint.LastCompileMethod=Unknown
RiderGameControl.TimeStamp=2025.05.12-16.50.14
RiderGameControl.LastCompileMethod=Unknown
PortableObjectFileDataSource.TimeStamp=2025.03.24-15.06.57
PortableObjectFileDataSource.LastCompileMethod=Unknown
ObjectMixerEditor.TimeStamp=2025.03.24-15.06.57
ObjectMixerEditor.LastCompileMethod=Unknown
LightMixer.TimeStamp=2025.03.24-15.06.57
LightMixer.LastCompileMethod=Unknown
SkeletalMeshModelingTools.TimeStamp=2025.03.24-15.07.13
SkeletalMeshModelingTools.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.03.24-15.02.11
SkeletalMeshEditor.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.03.24-15.06.57
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.03.24-15.01.58
CollectionManager.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.03.24-15.06.57
ContentBrowserClassDataSource.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.03.24-15.06.57
ContentBrowserFileDataSource.LastCompileMethod=Unknown
BaseCharacterFXEditor.TimeStamp=2025.03.24-15.07.13
BaseCharacterFXEditor.LastCompileMethod=Unknown
XInputDevice.TimeStamp=2025.03.24-15.08.30
XInputDevice.LastCompileMethod=Unknown
ConcertSyncClient.TimeStamp=2025.03.24-15.06.55
ConcertSyncClient.LastCompileMethod=Unknown
Inkpot.TimeStamp=2025.05.28-11.42.33
Inkpot.LastCompileMethod=Unknown
InkPlusPlus.TimeStamp=2025.06.05-12.24.11
InkPlusPlus.LastCompileMethod=External
InkpotEditor.TimeStamp=2025.05.28-11.42.33
InkpotEditor.LastCompileMethod=Unknown
SnappingHelper.TimeStamp=2025.05.28-11.35.11
SnappingHelper.LastCompileMethod=Unknown
Bridge.TimeStamp=2024.12.26-21.35.25
Bridge.LastCompileMethod=Unknown
MegascansPlugin.TimeStamp=2024.12.26-21.35.26
MegascansPlugin.LastCompileMethod=Unknown
CmdLinkServer.TimeStamp=2025.03.24-15.06.55
CmdLinkServer.LastCompileMethod=Unknown
Fab.TimeStamp=2025.02.17-15.51.43
Fab.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.03.24-15.08.09
AudioSynesthesiaEditor.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.03.24-15.08.40
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.03.24-15.08.41
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.03.24-15.08.40
TakeRecorderSources.LastCompileMethod=Unknown
CacheTrackRecorder.TimeStamp=2025.03.24-15.08.40
CacheTrackRecorder.LastCompileMethod=Unknown
DataflowEditor.TimeStamp=2025.03.24-15.07.20
DataflowEditor.LastCompileMethod=Unknown
ProfileVisualizer.TimeStamp=2025.03.24-15.02.09
ProfileVisualizer.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.03.24-15.02.05
ImageWriteQueue.LastCompileMethod=Unknown
TypedElementRuntime.TimeStamp=2025.03.24-15.02.14
TypedElementRuntime.LastCompileMethod=Unknown
LevelInstanceEditor.TimeStamp=2025.03.24-15.02.05
LevelInstanceEditor.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.03.24-15.01.55
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.03.24-15.02.07
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.03.24-15.01.55
AITestSuite.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.03.24-15.02.03
GameplayDebugger.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.03.24-15.02.07
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.03.24-15.02.09
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.03.24-15.02.09
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.03.24-15.01.55
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.03.24-15.02.05
LauncherPlatform.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.03.24-15.01.56
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixer.TimeStamp=2025.03.24-15.01.56
AudioMixer.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.03.24-15.01.56
AudioMixerCore.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.03.24-15.02.14
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.03.24-15.02.07
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.03.24-15.02.07
MovieSceneTracks.LastCompileMethod=Unknown
CinematicCamera.TimeStamp=2025.03.24-15.01.58
CinematicCamera.LastCompileMethod=Unknown
SparseVolumeTexture.TimeStamp=2025.03.24-15.02.11
SparseVolumeTexture.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.03.24-15.01.59
Documentation.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.03.24-15.02.07
OutputLog.LastCompileMethod=Unknown
SourceControlWindows.TimeStamp=2025.03.24-15.02.11
SourceControlWindows.LastCompileMethod=Unknown
SourceControlWindowExtender.TimeStamp=2025.03.24-15.02.11
SourceControlWindowExtender.LastCompileMethod=Unknown
UncontrolledChangelists.TimeStamp=2025.03.24-15.02.15
UncontrolledChangelists.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.03.24-15.01.58
ClassViewer.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.03.24-15.02.14
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.03.24-15.02.03
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.03.24-15.02.05
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.03.24-15.02.05
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.03.24-15.02.08
Persona.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.03.24-15.01.55
AdvancedPreviewScene.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.03.24-15.02.07
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.03.24-15.01.59
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.03.24-15.01.58
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.03.24-15.02.05
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.03.24-15.01.56
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.03.24-15.01.56
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.03.24-15.01.59
DeviceManager.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.03.24-15.02.11
SessionFrontend.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.03.24-15.02.11
ProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.03.24-15.02.11
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.03.24-15.01.59
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.03.24-15.02.05
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.03.24-15.02.11
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.03.24-15.02.11
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.03.24-15.01.57
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.03.24-15.02.18
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.03.24-15.02.15
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.03.24-15.01.59
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.03.24-15.02.03
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.03.24-15.02.06
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.03.24-15.02.06
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.03.24-15.02.07
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.03.24-15.02.05
InputBindingEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.03.24-15.01.59
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
InteractiveToolsFramework.TimeStamp=2025.03.24-15.02.05
InteractiveToolsFramework.LastCompileMethod=Unknown
StaticMeshEditor.TimeStamp=2025.03.24-15.02.14
StaticMeshEditor.LastCompileMethod=Unknown
EditorFramework.TimeStamp=2025.03.24-15.01.59
EditorFramework.LastCompileMethod=Unknown
EditorConfig.TimeStamp=2025.03.24-15.01.59
EditorConfig.LastCompileMethod=Unknown
DerivedDataEditor.TimeStamp=2025.03.24-15.01.59
DerivedDataEditor.LastCompileMethod=Unknown
CSVtoSVG.TimeStamp=2025.03.24-15.01.58
CSVtoSVG.LastCompileMethod=Unknown
VirtualizationEditor.TimeStamp=2025.03.24-15.02.16
VirtualizationEditor.LastCompileMethod=Unknown
AnimationSettings.TimeStamp=2025.03.24-15.01.55
AnimationSettings.LastCompileMethod=Unknown
GameplayDebuggerEditor.TimeStamp=2025.03.24-15.02.03
GameplayDebuggerEditor.LastCompileMethod=Unknown
RenderResourceViewer.TimeStamp=2025.03.24-15.02.11
RenderResourceViewer.LastCompileMethod=Unknown
StructUtilsEditor.TimeStamp=2025.03.24-15.02.14
StructUtilsEditor.LastCompileMethod=Unknown
StructUtilsTestSuite.TimeStamp=2025.03.24-15.02.14
StructUtilsTestSuite.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.03.24-15.01.37
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.03.24-15.01.45
IOSRuntimeSettings.LastCompileMethod=Unknown
MacPlatformEditor.TimeStamp=2025.03.24-15.02.06
MacPlatformEditor.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.03.24-15.02.18
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.03.24-15.01.37
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.03.24-15.01.37
AndroidDeviceDetection.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.03.24-15.02.08
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.03.24-15.01.45
IOSPlatformEditor.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.03.24-15.02.06
LogVisualizer.LastCompileMethod=Unknown
WidgetRegistration.TimeStamp=2025.03.24-15.02.16
WidgetRegistration.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.03.24-15.01.58
ClothPainter.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.03.24-15.02.16
ViewportInteraction.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.03.24-15.02.00
EditorWidgets.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.03.24-15.02.16
ViewportSnapping.LastCompileMethod=Unknown
MeshPaint.TimeStamp=2025.03.24-15.02.07
MeshPaint.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.03.24-15.02.08
PlacementMode.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.03.24-15.02.11
SessionServices.LastCompileMethod=Unknown
AndroidMediaEditor.TimeStamp=2025.03.24-15.07.48
AndroidMediaEditor.LastCompileMethod=Unknown
AndroidMediaFactory.TimeStamp=2025.03.24-15.07.48
AndroidMediaFactory.LastCompileMethod=Unknown
AvfMediaEditor.TimeStamp=2025.03.24-15.07.48
AvfMediaEditor.LastCompileMethod=Unknown
AvfMediaFactory.TimeStamp=2025.03.24-15.07.48
AvfMediaFactory.LastCompileMethod=Unknown
ImgMediaEditor.TimeStamp=2025.03.24-15.07.51
ImgMediaEditor.LastCompileMethod=Unknown
ImgMediaFactory.TimeStamp=2025.03.24-15.07.51
ImgMediaFactory.LastCompileMethod=Unknown
OpenExrWrapper.TimeStamp=2025.03.24-15.07.51
OpenExrWrapper.LastCompileMethod=Unknown
MediaCompositingEditor.TimeStamp=2025.03.24-15.07.51
MediaCompositingEditor.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.03.24-15.02.11
SequenceRecorder.LastCompileMethod=Unknown
MediaPlayerEditor.TimeStamp=2025.03.24-15.07.51
MediaPlayerEditor.LastCompileMethod=Unknown
WmfMediaEditor.TimeStamp=2025.03.24-15.07.54
WmfMediaEditor.LastCompileMethod=Unknown
WmfMediaFactory.TimeStamp=2025.03.24-15.07.54
WmfMediaFactory.LastCompileMethod=Unknown
WebMMedia.TimeStamp=2025.03.24-15.07.54
WebMMedia.LastCompileMethod=Unknown
WebMMediaEditor.TimeStamp=2025.03.24-15.07.54
WebMMediaEditor.LastCompileMethod=Unknown
WebMMediaFactory.TimeStamp=2025.03.24-15.07.54
WebMMediaFactory.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.03.24-15.07.54
ActorSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.03.24-15.07.54
LevelSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.03.24-15.07.54
TemplateSequenceEditor.LastCompileMethod=Unknown
AndroidFileServerEditor.TimeStamp=2025.03.24-15.08.08
AndroidFileServerEditor.LastCompileMethod=Unknown
AudioCaptureEditor.TimeStamp=2025.03.24-15.08.08
AudioCaptureEditor.LastCompileMethod=Unknown
GooglePADEditor.TimeStamp=2025.03.24-15.08.17
GooglePADEditor.LastCompileMethod=Unknown
RigVMEditor.TimeStamp=2025.03.24-15.08.29
RigVMEditor.LastCompileMethod=Unknown
ResonanceAudioEditor.TimeStamp=2025.03.24-15.08.29
ResonanceAudioEditor.LastCompileMethod=Unknown
WaveTableEditor.TimeStamp=2025.03.24-15.08.30
WaveTableEditor.LastCompileMethod=Unknown
SmartSnapping.TimeStamp=2025.03.24-15.06.41
SmartSnapping.LastCompileMethod=Unknown
ControlRigEditor.TimeStamp=2025.03.24-15.06.42
ControlRigEditor.LastCompileMethod=Unknown
RewindDebugger.TimeStamp=2025.03.24-15.06.52
RewindDebugger.LastCompileMethod=Unknown
RewindDebuggerVLog.TimeStamp=2025.03.24-15.06.52
RewindDebuggerVLog.LastCompileMethod=Unknown
OptimusEditor.TimeStamp=2025.03.24-15.06.52
OptimusEditor.LastCompileMethod=Unknown
IKRigEditor.TimeStamp=2025.03.24-15.06.52
IKRigEditor.LastCompileMethod=Unknown
LiveLinkMultiUser.TimeStamp=2025.03.24-15.06.52
LiveLinkMultiUser.LastCompileMethod=Unknown
GameplayCamerasEditor.TimeStamp=2025.03.24-15.06.55
GameplayCamerasEditor.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.03.24-15.06.52
CameraShakePreviewer.LastCompileMethod=Unknown
EngineAssetDefinitions.TimeStamp=2025.03.24-15.06.57
EngineAssetDefinitions.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.03.24-15.06.57
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.03.24-15.06.57
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.03.24-15.06.57
TextureAlignMode.LastCompileMethod=Unknown
CharacterAI.TimeStamp=2025.03.24-15.07.13
CharacterAI.LastCompileMethod=Unknown
FractureEngine.TimeStamp=2025.03.24-15.07.20
FractureEngine.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.03.24-15.07.32
PlanarCut.LastCompileMethod=Unknown
RiderShaderInfo.TimeStamp=2025.05.12-16.50.00
RiderShaderInfo.LastCompileMethod=Unknown
RiderLC.TimeStamp=2025.05.12-16.50.01
RiderLC.LastCompileMethod=Unknown
rdBPtools.TimeStamp=2025.05.28-11.34.56
rdBPtools.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.03.24-15.01.55
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.03.24-15.02.11
SceneDepthPickerMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.03.24-15.02.05
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.03.24-15.02.03
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.03.24-15.02.16
VirtualTexturingEditor.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.03.24-15.01.56
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=2025.03.24-15.02.11
SequenceRecorderSections.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.03.24-15.02.14
StatsViewer.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.03.24-15.02.03
GameProjectGeneration.LastCompileMethod=Unknown
UnsavedAssetsTracker.TimeStamp=2025.03.24-15.02.16
UnsavedAssetsTracker.LastCompileMethod=Unknown
StatusBar.TimeStamp=2025.03.24-15.02.14
StatusBar.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.03.24-15.01.55
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.03.24-15.02.16
WidgetCarousel.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.03.24-15.02.11
SceneOutliner.LastCompileMethod=Unknown
SubobjectEditor.TimeStamp=2025.03.24-15.02.14
SubobjectEditor.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.03.24-15.02.03
HierarchicalLODOutliner.LastCompileMethod=Unknown
Voice.TimeStamp=2025.03.24-15.02.16
Voice.LastCompileMethod=Unknown
MovieSceneCapture.TimeStamp=2025.03.24-15.02.07
MovieSceneCapture.LastCompileMethod=Unknown
SkeletonEditor.TimeStamp=2025.03.24-15.02.11
SkeletonEditor.LastCompileMethod=Unknown
PinnedCommandList.TimeStamp=2025.03.24-15.02.08
PinnedCommandList.LastCompileMethod=Unknown
AnimationEditor.TimeStamp=2025.03.24-15.01.55
AnimationEditor.LastCompileMethod=Unknown
SequencerWidgets.TimeStamp=2025.03.24-15.02.11
SequencerWidgets.LastCompileMethod=Unknown

[MessageLog]
LastLogListing=MapCheck

[AssetEditorSubsystem]
CleanShutdown=False
DebuggerAttached=False
RecentAssetEditors=ControlRigEditor
RecentAssetEditors=ControlRigEditor
RecentAssetEditors=AnimationBlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=AnimationEditor
RecentAssetEditors=AnimationEditor
RecentAssetEditors=AnimationEditor
RecentAssetEditors=SkeletonEditor
RecentAssetEditors=SkeletonEditor
RecentAssetEditors=SkeletonEditor
RecentAssetEditors=SkeletalMeshEditor
RecentAssetEditors=AnimationBlueprintEditor
RecentAssetEditors=AnimationEditor
RecentAssetEditors=AnimationEditor
RecentAssetEditors=PoseSearchDatabaseEditor
RecentAssetEditors=ChooserTableEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=AnimationEditor
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
OpenAssetsAtExit=/Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
OpenAssetsAtExit=/Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
OpenAssetsAtExit=/Game/Characters/BaoliMC/CR_AO.CR_AO
OpenAssetsAtExit=/Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager

[RootWindow]
ScreenPosition=X=1280.000 Y=672.000
WindowSize=X=2554.000 Y=1434.000
InitiallyMaximized=False

[SlateAdditionalLayoutConfig]
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[ContentBrowser]
ContentBrowserTab1.SourcesExpanded=True
ContentBrowserTab1.IsLocked=False
ContentBrowserTab1.FavoritesAreaExpanded=False
ContentBrowserTab1.PathAreaExpanded=True
ContentBrowserTab1.CollectionAreaExpanded=False
ContentBrowserTab1.FavoritesSearchAreaExpanded=False
ContentBrowserTab1.PathSearchAreaExpanded=False
ContentBrowserTab1.CollectionSearchAreaExpanded=False
ContentBrowserTab1.VerticalSplitter.FixedSlotSize0=150
ContentBrowserTab1.VerticalSplitter.SlotSize1=0.75
ContentBrowserTab1.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab1.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab1.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab1.SelectedPaths=/Game/Blueprints
ContentBrowserTab1.PluginFilters=
ContentBrowserTab1.Favorites.SelectedPaths=
FavoritePaths=
ContentBrowserTab1.SelectedCollections=
ContentBrowserTab1.ExpandedCollections=
ContentBrowserTab1.ThumbnailSize=1
ContentBrowserTab1.CurrentViewType=1
ContentBrowserTab1.ZoomScale=0
SequenceBrowser.ThumbnailSize=2
SequenceBrowser.CurrentViewType=2
SequenceBrowser.ZoomScale=0
SequenceBrowser.HiddenColumns=Source Frame Rate
SequenceBrowser.HiddenColumns=Number of Frames
SequenceBrowser.HiddenColumns=Number of Keys
SequenceBrowser.HiddenColumns=AnimSyncMarkerList
SequenceBrowser.HiddenColumns=Compressed Size (KB)
SequenceBrowser.HiddenColumns=Target Frame Rate
SequenceBrowser.HiddenColumns=ImportFileFramerate
SequenceBrowser.HiddenColumns=ImportResampleFramerate
SequenceBrowser.HiddenColumns=AdditiveAnimType
SequenceBrowser.HiddenColumns=RetargetSource
SequenceBrowser.HiddenColumns=RetargetSourceAsset
SequenceBrowser.HiddenColumns=Interpolation
SequenceBrowser.HiddenColumns=bEnableRootMotion
SequenceBrowser.HiddenColumns=bUseNormalizedRootMotionScale
SequenceBrowser.HiddenColumns=PlatformTargetFrameRate
SequenceBrowser.HiddenColumns=NumberOfSampledKeys
SequenceBrowser.HiddenColumns=SequenceLength
SequenceBrowser.HiddenColumns=Skeleton
SequenceBrowser.HiddenColumns=ParentAsset
SequenceBrowser.HiddenColumns=PreviewSkeletalMesh
SequenceBrowser.HiddenColumns=HasParentAsset
SequenceBrowser.HiddenColumns=AnimNotifyList
SequenceBrowser.HiddenColumns=CurveNameList
SequenceBrowser.HiddenColumns=Class
ContentBrowserDrawer.SourcesExpanded=True
ContentBrowserDrawer.IsLocked=False
ContentBrowserDrawer.FavoritesAreaExpanded=False
ContentBrowserDrawer.PathAreaExpanded=True
ContentBrowserDrawer.CollectionAreaExpanded=False
ContentBrowserDrawer.FavoritesSearchAreaExpanded=False
ContentBrowserDrawer.PathSearchAreaExpanded=False
ContentBrowserDrawer.CollectionSearchAreaExpanded=False
ContentBrowserDrawer.VerticalSplitter.FixedSlotSize0=150
ContentBrowserDrawer.VerticalSplitter.SlotSize1=0.75
ContentBrowserDrawer.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserDrawer.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserDrawer.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserDrawer.SelectedPaths=/Game/Blueprints
ContentBrowserDrawer.PluginFilters=
ContentBrowserDrawer.Favorites.SelectedPaths=
ContentBrowserDrawer.SelectedCollections=
ContentBrowserDrawer.ExpandedCollections=
ContentBrowserDrawer.ThumbnailSize=2
ContentBrowserDrawer.CurrentViewType=1
ContentBrowserDrawer.ZoomScale=0
ContentBrowserDrawer.JumpMRU=/All/Game/Characters
ContentBrowserDrawer.JumpMRU=/All/Game/Characters/UEFN_Mannequin/Animations/AimOffset
ContentBrowserDrawer.JumpMRU=/All/Game
ContentBrowserDrawer.JumpMRU=/All/Game/Blueprints
ContentBrowserDrawer.JumpMRU=/All/Game/Characters/UEFN_Mannequin/Animations/Walk
ContentBrowserDrawer.JumpMRU=/All/Game/Characters/UEFN_Mannequin/Animations
ContentBrowserDrawer.JumpMRU=/All/Game/Characters/UEFN_Mannequin/Animations/Run
ContentBrowserDrawer.JumpMRU=/All/Game/Characters/UEFN_Mannequin
ContentBrowserDrawer.JumpMRU=/All/Game/Input
ContentBrowserTab1.JumpMRU=/All/Game/Blueprints
ContentBrowserTab1.JumpMRU=/All/Game
ContentBrowserTab1.JumpMRU=/All/Game/Characters/UEFN_Mannequin/Animations/AimOffset
ContentBrowserTab1.JumpMRU=/All/Game/Characters
ContentBrowserTab1.JumpMRU=/All/Game/Characters/BaoliMC
ContentBrowserTab1.JumpMRU=/All
ContentBrowserTab1.JumpMRU=/All/Game/Levels
ContentBrowserTab1.JumpMRU=/All/Game/Input
GlobalAssetPicker.ThumbnailSize=2
GlobalAssetPicker.CurrentViewType=0
GlobalAssetPicker.ZoomScale=0
AssetPropertyPicker.ThumbnailSize=2
AssetPropertyPicker.CurrentViewType=0
AssetPropertyPicker.ZoomScale=0

[Directories2]
UNR=H:/P4/dev/Baoli/Content/Levels
BRUSH=H:/P4/dev/Baoli/Content/
FBX=H:/P4/dev/Baoli/Content/
FBXAnim=H:/P4/dev/Baoli/Content/
GenericImport=H:/P4/dev/Baoli/Content/
GenericExport=H:/P4/dev/Baoli/Content/
GenericOpen=H:/P4/dev/Baoli/Content/
GenericSave=H:/P4/dev/Baoli/Content/
MeshImportExport=H:/P4/dev/Baoli/Content/
WorldRoot=H:/P4/dev/Baoli/Content/
Level=H:/P4/dev/Baoli/Content/
Project=D:/UE_5.5/

[/Script/RewindDebuggerVLog.RewindDebuggerVLogSettings]
DisplayVerbosity=4
DisplayCategories=()

[/Script/RewindDebugger.RewindDebuggerSettings]
CameraMode=Replay
bShouldAutoEject=False
bShouldAutoRecordOnPIE=False
PlaybackRate=1.000000
bShowEmptyObjectTracks=False
DebugTargetActor=

[/Script/ChooserEditor.ChooserEditorSettings]
DefaultCreateType=

[Python]
LastDirectory=
RecentsFiles=D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py
RecentsFiles=D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py

[/Script/AudioEditor.AudioEditorSettings]
bUseAudioAttenuation=True
bPinSoundCueInAssetMenu=True
bPinSoundCueTemplateInAssetMenu=False
bPinSoundAttenuationInAssetMenu=True
bPinSoundConcurrencyInAssetMenu=True

[DetailPropertyExpansion]
PreviewMeshCollection="\"Object\" \"Object.SkeletalMeshes\" "
DataAsset="\"Object\" \"Object.SkeletalMeshes\" "
Object="\"Object\" "
ControlRigWrapperObject_-**********="\"Object\" \"Object.Nodes.Item\" "
ControlRigWrapperObject="\"Object\" "
RigVMDetailsViewWrapperObject="\"Object\" "
ControlRigWrapperObject_-**********="\"Object\" \"Object.Nodes.Item\" \"Object.Nodes.Value\" "
StaticMeshActor="\"Object.StaticMeshActor.StaticMeshComponent.Object.Collision.BodyInstance\" "
BP_BaoliCharacter_C="\"Object.Character.Mesh.Object.Collision.BodyInstance\" "
Baoli_Character="\"Object.Character.Mesh.Object.Collision.BodyInstance\" "
Character="\"Object.Character.Mesh.Object.Collision.BodyInstance\" "
Pawn="\"Object.Character.Mesh.Object.Collision.BodyInstance\" "
SkeletalMeshComponent="\"Object\" "
SkinnedMeshComponent="\"Object\" "
MeshComponent="\"Object\" "
PrimitiveComponent="\"Object\" "
SceneComponent="\"Object\" "
ActorComponent="\"Object\" "
ChooserTable="\"Object.Parameters.ContextData.ContextData[0]\" "
BlendSpace="\"Object\" \"Object.SampleSmoothing.ManualPerBoneOverrides\" \"Object.SampleSmoothing.ManualPerBoneOverrides.ManualPerBoneOverrides[0]\" \"Object.BlendParametersTest.BlendParameters.BlendParameters[0]\" \"Object.BlendParametersTest.BlendParameters.BlendParameters[1]\" "
AnimationAsset="\"Object\" "
ABP_SandboxCharacter_C="\"Object\" "
AnimInstance="\"Object\" "
ControlRigWrapperObject_-592631711="\"Object\" \"Object.Nodes.Item\" \"Object.Nodes.Primary\" \"Object.Nodes.Secondary\" "
ControlRigWrapperObject_-619337892="\"Object.Nodes.Elements\" "
ControlRigWrapperObject_-1314496310="\"Object.Nodes.Value\" "
BP_BaoliController_C="\"Object.Default.EmailInboxStruct\" \"Object.Default.EmailInboxStruct.EmailInboxStruct[0]\" \"Object.Default.EmailInboxStruct.EmailInboxStruct[1]\" \"Object.Default.EmailInboxStruct.EmailInboxStruct[2]\" \"Object.Replication.ReplicatedMovement\" "
Baoli_Controller="\"Object.Default.EmailInboxStruct\" \"Object.Default.EmailInboxStruct.EmailInboxStruct[0]\" \"Object.Default.EmailInboxStruct.EmailInboxStruct[1]\" \"Object.Default.EmailInboxStruct.EmailInboxStruct[2]\" \"Object.Replication.ReplicatedMovement\" "
PlayerController=
Controller=
StaticMeshComponent="\"Object.Collision.BodyInstance\" "
EdGraphNode_Comment="\"Object\" \"Object.Comment.CommentColor\" "
EdGraphNode="\"Object\" "
ControlRigWrapperObject_-1089398397="\"Object.Nodes.ItemToModify\" "
CapsuleComponent="\"Object.Collision.BodyInstance\" "
ShapeComponent="\"Object.Collision.BodyInstance\" "
ControlRigWrapperObject_2089149577="\"Object\" \"Object.Nodes.Value\" "
AnimGraphNode_LayeredBoneBlend="\"Object\" \"Object.Settings.Node.LayerSetup\" \"Object.Settings.Node.LayerSetup.LayerSetup[0]\" \"Object.Settings.Node.LayerSetup.LayerSetup[0].BranchFilters\" "
AnimGraphNode_BlendListBase="\"Object\" "
AnimGraphNode_Base="\"Object\" "
K2Node="\"Object\" "
AnimGraphNode_BlendSpaceGraph="\"Object\" "
AnimGraphNode_BlendSpaceGraphBase="\"Object\" "
BlendSpaceGraph="\"Object\" "
EdGraph="\"Object\" "
AnimGraphNode_BlendSpacePlayer="\"Object\" "
AnimGraphNode_BlendSpaceBase="\"Object\" "
AnimGraphNode_AssetPlayerBase="\"Object\" "
AnimGraphNode_SaveCachedPose="\"Object\" "
K2Node_MakeStruct="\"Object\" "
K2Node_StructMemberSet="\"Object\" "
K2Node_StructOperation="\"Object\" "
K2Node_Variable="\"Object\" "
EulerAnalysisProperties="\"Object\" "
AnalysisProperties="\"Object\" "
AnimGraphNode_CallFunction="\"Object\" "
PersonaPreviewSceneDescription="\"Object\" "
AssetViewerSettings="\"Object\" "
AnimGraphNode_ControlRig="\"Object\" "
AnimGraphNode_CustomProperty="\"Object\" "
AnimGraphNode_ApplyMeshSpaceAdditive="\"Object\" "
AnimGraphNode_DeadBlending="\"Object\" "
AnimGraphNode_LocalToComponentSpace="\"Object\" "
CR_HeadManager_C="\"Object\" "
ControlRig="\"Object\" "
RigVMHost="\"Object\" "
AnimGraphNode_UseCachedPose="\"Object\" "
AnimGraphNode_BlendListByBool="\"Object\" "
REINST_CR_HeadManager_C_8="\"Object\" "
AnimSequence="\"Object\" "
AnimSequenceBase="\"Object\" "
K2Node_SetFieldsInStruct="\"Object\" "
AnimGraphNode_BlendSpaceEvaluator="\"Object\" "
CR_AO_C="\"Object\" "
ControlRigWrapperObject_FRigControlElement="\"Object\" "
ControlRigWrapperObject_1533679343="\"Object\" "
ControlRigWrapperObject_FRigBoneElement="\"Object\" "
ControlRigWrapperObject_786182937="\"Object\" \"Object.Nodes.Parent\" \"Object.Nodes.Value\" "
ControlRigWrapperObject_289404103="\"Object\" \"Object.Nodes.Child\" \"Object.Nodes.Parent\" "
ControlRigWrapperObject_-1021361431="\"Object\" "
ControlRigWrapperObject_1547712147="\"Object\" "
ControlRigWrapperObject_-1410011600="\"Object\" "
ControlRigWrapperObject_-674762563="\"Object\" "
ControlRigWrapperObject_582848483="\"Object\" "
ControlRigWrapperObject_-1714465748="\"Object\" \"Object.Nodes.Value\" "
AnimGraphNode_BlendListByInt="\"Object\" "
ControlRigWrapperObject_1164909996="\"Object\" "
ControlRigWrapperObject_-697724807="\"Object\" \"Object.Nodes.Child\" \"Object.Nodes.WorldUp\" \"Object.Nodes.Parents\" "
ControlRigWrapperObject_-1723610182="\"Object\" "
ControlRigWrapperObject_331962474="\"Object\" "
REINST_CR_AO_C_12="\"Object\" "
REINST_CR_HeadManager_C_14="\"Object\" "
ControlRigWrapperObject_-342214179="\"Object\" "
CameraComponent="\"Object\" "

[AssetEditorSubsystemRecents]
MRUItem0=/Game/Blueprints/BP_BaoliCharacter
MRUItem1=/Game/Characters/BaoliMC/CR_HeadManager
MRUItem2=/Game/Characters/BaoliMC/CR_AO
MRUItem3=/Game/Blueprints/ABP_SandboxCharacter
MRUItem4=/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_F_R_180
MRUItem5=/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand
MRUItem6=/Game/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+135_Y0
MRUItem7=/Game/Characters/UEFN_Mannequin/Meshes/SK_UEFN_Mannequin
MRUItem8=/Game/Characters/Animations/CasRetarget/Cas_Skeleton
MRUItem9=/Game/MetaHumans/Common/Female/Medium/NormalWeight/Body/metahuman_base_skel
MRUItem10=/Game/Characters/BaoliMC/JoinHuman
MRUItem11=/Game/Levels/HouseLevel
MRUItem12=/Game/Characters/UEFN_Mannequin/Animations/Idle/M_Neutral_Stand_Idle_Break_v04
MRUItem13=/Game/Characters/UEFN_Mannequin/Animations/Idle/M_Neutral_Stand_Idle_Loop
MRUItem14=/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/Databases/Dense/PSD_Dense_Stand_Idles
MRUItem15=/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense
MRUItem16=/Game/Blueprints/BP_ChestActorPawn
MRUItem17=/Game/Blueprints/BaoliPlayerCameraManager
MRUItem18=/Game/Blueprints/BP_BaoliController
MRUItem19=/Game/Characters/Animations/CasRetarget/Cas
MRUItem20=/Game/Input/IA_Look
MRUItem21=/Game/Input/IMC_GDCMotionMatching
MRUItem22=/Game/Input/IA_phuphu
MRUItem23=/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/Databases/Dense/PSD_Dense_Stand_TurnInPlace
MRUItem24=/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Sparse
MRUItem25=/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases

[DetailCategories]
ABP_SandboxCharacter_C.References=True
ABP_SandboxCharacter_C.Essential Values=True
ABP_SandboxCharacter_C.States=True
ABP_SandboxCharacter_C.Trajectory=True
ABP_SandboxCharacter_C.Flashlight=True
ABP_SandboxCharacter_C.UserBasedBools=True
ABP_SandboxCharacter_C.RootMotion=True
ABP_SandboxCharacter_C.Notifies=True
ABP_SandboxCharacter_C.Montage=True
AnimGraphNode_ControlRig.Functions=True
AnimGraphNode_ControlRig.Settings=True
AnimGraphNode_ControlRig.Input=True
AnimGraphNode_ControlRig.Tag=True
AnimGraphNode_ControlRig.Output=True
AnimGraphNode_ControlRig.ControlRig=True
AnimGraphNode_ControlRig.Performance=True
AnimGraphNode_ControlRig.Bindings=True
ControlRigWrapperObject_-**********.Nodes=True
ControlRigWrapperObject_318700421.Nodes=True
ControlRigWrapperObject_1926573860.Nodes=True
ControlRigWrapperObject_-1045578206.Nodes=True
ControlRigWrapperObject_1458343674.Nodes=True
ControlRigWrapperObject_-**********.Nodes=True
StaticMeshActor.TransformCommon=True
StaticMeshActor.StaticMesh=True
StaticMeshActor.Materials=True
StaticMeshActor.Physics=True
StaticMeshActor.Collision=True
StaticMeshActor.Lighting=True
StaticMeshActor.Rendering=True
StaticMeshActor.Mesh Painting=True
StaticMeshActor.HLOD=True
StaticMeshActor.Navigation=True
StaticMeshActor.VirtualTexture=True
StaticMeshActor.Tags=True
StaticMeshActor.Cooking=True
StaticMeshActor.Replication=True
StaticMeshActor.Networking=True
StaticMeshActor.Actor=True
BP_BaoliCharacter_C.TransformCommon=True
BP_BaoliCharacter_C.Animation=True
BP_BaoliCharacter_C.Mesh=True
BP_BaoliCharacter_C.Default=True
BP_BaoliCharacter_C.Tick=True
BP_BaoliCharacter_C.Flashlight=True
BP_BaoliCharacter_C.ComponentTick=True
BP_BaoliCharacter_C.Camera=True
BP_BaoliCharacter_C.InspectionLight=True
BP_BaoliCharacter_C.Physics=True
BP_BaoliCharacter_C.InpsectionLight=True
BP_BaoliCharacter_C.Collision=True
BP_BaoliCharacter_C.Input=True
BP_BaoliCharacter_C.Lighting=True
BP_BaoliCharacter_C.Movement=True
BP_BaoliCharacter_C.Sensitivity=True
BP_BaoliCharacter_C.Animation Variables=True
BP_BaoliCharacter_C.Flashlight Variables=True
BP_BaoliCharacter_C.SkinWeights=True
BP_BaoliCharacter_C.Rendering=True
BP_BaoliCharacter_C.Clothing=True
BP_BaoliCharacter_C.LeaderPoseComponent=True
BP_BaoliCharacter_C.AnimationRig=True
BP_BaoliCharacter_C.Deformer=True
BP_BaoliCharacter_C.HLOD=True
BP_BaoliCharacter_C.Mobile=True
BP_BaoliCharacter_C.Navigation=True
BP_BaoliCharacter_C.RayTracing=True
BP_BaoliCharacter_C.VirtualTexture=True
BP_BaoliCharacter_C.Tags=True
BP_BaoliCharacter_C.AssetUserData=True
BP_BaoliCharacter_C.Replication=True
BP_BaoliCharacter_C.ComponentReplication=True
BP_BaoliCharacter_C.Activation=True
BP_BaoliCharacter_C.Variable=True
BP_BaoliCharacter_C.Cooking=True
BP_BaoliCharacter_C.Character Movement (General Settings)=True
BP_BaoliCharacter_C.Character Movement: Walking=True
BP_BaoliCharacter_C.Character Movement: Jumping / Falling=True
BP_BaoliCharacter_C.Character Movement: Gravity=True
BP_BaoliCharacter_C.Character Movement (Networking)=True
BP_BaoliCharacter_C.Character Movement: Swimming=True
BP_BaoliCharacter_C.Character Movement: Flying=True
BP_BaoliCharacter_C.Character Movement: Custom Movement=True
BP_BaoliCharacter_C.Character Movement (Rotation Settings)=True
BP_BaoliCharacter_C.Character Movement: Physics Interaction=True
BP_BaoliCharacter_C.Character Movement=True
BP_BaoliCharacter_C.Character Movement: Avoidance=True
BP_BaoliCharacter_C.RootMotion=True
BP_BaoliCharacter_C.Character Movement: NavMesh Movement=True
BP_BaoliCharacter_C.NavMovement=True
BP_BaoliCharacter_C.Velocity=True
BP_BaoliCharacter_C.PlanarMovement=True
BP_BaoliCharacter_C.MovementComponent=True
BP_BaoliCharacter_C.Shape=True
BP_BaoliCharacter_C.Character=True
BP_BaoliCharacter_C.Pawn=True
BP_BaoliCharacter_C.Actor=True
BP_BaoliCharacter_C.Events=True
BP_BaoliCharacter_C.Materials=True
PersonaPreviewSceneDescription.Animation=True
PersonaPreviewSceneDescription.Mesh=True
PersonaPreviewSceneDescription.Physics=True
PersonaPreviewSceneDescription.Additional Meshes=True
AssetViewerSettings.Settings=True
AnimGraphNode_ApplyMeshSpaceAdditive.Functions=True
AnimGraphNode_ApplyMeshSpaceAdditive.Settings=True
AnimGraphNode_ApplyMeshSpaceAdditive.Performance=True
AnimGraphNode_ApplyMeshSpaceAdditive.Tag=True
AnimGraphNode_ApplyMeshSpaceAdditive.Bindings=True
BP_BaoliCharacter_C.SkeletalMesh=True
SpringArmComponent.Variable=True
SpringArmComponent.TransformCommon=True
SpringArmComponent.Sockets=True
SpringArmComponent.Camera=True
SpringArmComponent.CameraCollision=True
SpringArmComponent.CameraSettings=True
SpringArmComponent.ComponentTick=True
SpringArmComponent.Lag=True
SpringArmComponent.Rendering=True
SpringArmComponent.Tags=True
SpringArmComponent.ComponentReplication=True
SpringArmComponent.Activation=True
SpringArmComponent.Cooking=True
SpringArmComponent.Events=True
AnimGraphNode_BlendSpacePlayer.Sync=True
AnimGraphNode_BlendSpacePlayer.Functions=True
AnimGraphNode_BlendSpacePlayer.Relevancy=True
AnimGraphNode_BlendSpacePlayer.Coordinates=False
AnimGraphNode_BlendSpacePlayer.Tag=True
AnimGraphNode_BlendSpacePlayer.Settings=True
AnimGraphNode_BlendSpacePlayer.Bindings=True
BlendSpace.InputInterpolation=True
BlendSpace.Axis Settings=True
BlendSpace.Analysis=True
BlendSpace.BlendSamples=True
BlendSpace.SampleSmoothing=True
BlendSpace.Animation=True
BlendSpace.AdditiveSettings=True
BlendSpace.AnimationNotifies=True
BlendSpace.MetaData=True
BlendSpace.Thumbnail=True
EdGraph.Graph=True
EdGraph.Inputs=True
EdGraph.Outputs=True
AnimGraphNode_BlendListByBool.Functions=True
AnimGraphNode_BlendListByBool.Runtime=True
AnimGraphNode_BlendListByBool.Tag=True
AnimGraphNode_BlendListByBool.Config=True
AnimGraphNode_BlendListByBool.BlendType=True
AnimGraphNode_BlendListByBool.Option=True
AnimGraphNode_BlendListByBool.Bindings=True
K2Node_VariableGet.Variable=True
K2Node_VariableGet.DefaultValueCategory=True
K2Node_VariableGet.Events=True
AnimGraphNode_DeadBlending.Functions=True
AnimGraphNode_DeadBlending.Blending=True
AnimGraphNode_DeadBlending.Tag=True
AnimGraphNode_DeadBlending.Filter=True
AnimGraphNode_DeadBlending.Extrapolation=True
AnimGraphNode_DeadBlending.Debug=True
AnimGraphNode_DeadBlending.Bindings=True
K2Node_CallFunction.Graph=True
K2Node_CallFunction.Inputs=True
K2Node_CallFunction.Outputs=True
EdGraphNode_Comment.GraphNodeDetail=True
EdGraphNode_Comment.Comment=True
K2Node_FunctionEntry.GraphNodeDetail=True
K2Node_FunctionEntry.Graph=True
K2Node_FunctionEntry.Inputs=True
K2Node_FunctionEntry.Outputs=True
AnimGraphNode_Base.Functions=True
AnimGraphNode_Base.Tag=True
AnimGraphNode_Base.Bindings=True
CameraComponent.Variable=True
CameraComponent.TransformCommon=True
CameraComponent.Sockets=True
CameraComponent.CameraSettings=True
CameraComponent.CameraOptions=True
CameraComponent.Camera=True
CameraComponent.ComponentTick=True
CameraComponent.PostProcess=True
CameraComponent.Tags=True
CameraComponent.ComponentReplication=True
CameraComponent.Activation=True
CameraComponent.Cooking=True
CameraComponent.Events=True
SkeletalMeshComponent.Variable=True
SkeletalMeshComponent.TransformCommon=True
SkeletalMeshComponent.Sockets=True
SkeletalMeshComponent.Animation=True
SkeletalMeshComponent.Mesh=True
SkeletalMeshComponent.ComponentTick=True
SkeletalMeshComponent.Clothing=True
SkeletalMeshComponent.LeaderPoseComponent=True
SkeletalMeshComponent.Physics=True
SkeletalMeshComponent.AnimationRig=True
SkeletalMeshComponent.Collision=True
SkeletalMeshComponent.Deformer=True
SkeletalMeshComponent.Lighting=True
SkeletalMeshComponent.HLOD=True
SkeletalMeshComponent.SkinWeights=True
SkeletalMeshComponent.Rendering=True
SkeletalMeshComponent.Navigation=True
SkeletalMeshComponent.VirtualTexture=True
SkeletalMeshComponent.Tags=True
SkeletalMeshComponent.ComponentReplication=True
SkeletalMeshComponent.Activation=True
SkeletalMeshComponent.Cooking=True
SkeletalMeshComponent.Events=True
SkeletalMeshComponent.Materials=True
SkeletalMeshComponent.Optimization=True
PoseSearchDatabase.Database=True
PoseSearchDatabase.Preview=True
PoseSearchDatabase.Performance=True
ChooserTable.Result=True
ChooserTable.Parameters=True
PoseSearchDatabaseSequenceReflection.Selected Sequence=True
ControlRigWrapperObject_FRigBoneElement.General=True
ControlRigWrapperObject_FRigBoneElement.Transform=True
InputAction.Description=True
InputAction.Action=True
InputAction.Input Consumption=True
InputAction.User Settings=True
InputMappingContext.Mappings=True
InputMappingContext.Description=True
ChooserRowDetails.Properties=True
PropertyWrapper.Variable=True
PropertyWrapper.DefaultValueCategory=True
ABP_SandboxCharacter_C.Default=True
K2Node_FunctionResult.Graph=True
K2Node_FunctionResult.Inputs=True
K2Node_FunctionResult.Outputs=True
REINST_CR_HeadManager_C_11.Default=True
REINST_CR_HeadManager_C_7.Default=True
K2Node_VariableSet.Variable=True
K2Node_VariableSet.DefaultValueCategory=True
CR_HeadManager_C.Default=True
BP_BaoliCharacter_C.LOD=False
BP_BaoliCharacter_C.TextureStreaming=False
BP_BaoliCharacter_C.WorldPartition=False
BP_BaoliCharacter_C.Optimization=False
BP_BaoliCharacter_C.MaterialParameters=False
BP_BaoliCharacter_C.LevelInstance=False
BP_BaoliCharacter_C.DataLayers=False
CameraComponent.Physics=False
CameraComponent.AssetUserData=False
CameraComponent.Replication=False
SpotLightComponent.Variable=True
SpotLightComponent.TransformCommon=True
SpotLightComponent.Sockets=True
SpotLightComponent.Light=True
SpotLightComponent.Rendering=True
SpotLightComponent.Lightmass=True
SpotLightComponent.Performance=True
SpotLightComponent.ComponentTick=True
SpotLightComponent.LightFunction=True
SpotLightComponent.DistanceFieldShadows=True
SpotLightComponent.RayTracing=True
SpotLightComponent.Light Profiles=True
SpotLightComponent.Tags=True
SpotLightComponent.ComponentReplication=True
SpotLightComponent.Cooking=True
SpotLightComponent.Events=True
K2Node_Timeline.GraphNodeDetail=True
BP_BaoliController_C.Tick=True
BP_BaoliController_C.Default=True
BP_BaoliController_C.SkillCheck=True
BP_BaoliController_C.GameStatus=True
BP_BaoliController_C.Input=True
BP_BaoliController_C.PlayerController=True
BP_BaoliController_C.Cheat Manager=True
BP_BaoliController_C.MouseInterface=True
BP_BaoliController_C.Game=True
BP_BaoliController_C.WorldPartition=True
BP_BaoliController_C.Controller=True
BP_BaoliController_C.Replication=True
BP_BaoliController_C.Actor=True
BP_BaoliController_C.HLOD=True
BP_BaoliController_C.Physics=True
BP_BaoliController_C.Events=True
Blueprint.ClassOptions=True
Blueprint.BlueprintOptions=True
Blueprint.Imports=True
Blueprint.Interfaces=True
Blueprint.Thumbnail=True
AnimationGraph.Inputs=True
AnimationGraph.GraphBlending=True
ArrowComponent.Variable=True
ArrowComponent.TransformCommon=True
ArrowComponent.Sockets=True
ArrowComponent.ArrowComponent=True
ArrowComponent.HLOD=True
ArrowComponent.Rendering=True
ArrowComponent.ComponentTick=True
ArrowComponent.Navigation=True
ArrowComponent.Tags=True
ArrowComponent.ComponentReplication=True
ArrowComponent.Cooking=True
ArrowComponent.Events=True
BaoliPlayerCameraManager_C.TransformCommon=True
BaoliPlayerCameraManager_C.Tick=True
BaoliPlayerCameraManager_C.Rendering=True
BaoliPlayerCameraManager_C.Physics=True
BaoliPlayerCameraManager_C.ComponentTick=True
BaoliPlayerCameraManager_C.Tags=True
BaoliPlayerCameraManager_C.Replication=True
BaoliPlayerCameraManager_C.ComponentReplication=True
BaoliPlayerCameraManager_C.Activation=True
BaoliPlayerCameraManager_C.Variable=True
BaoliPlayerCameraManager_C.Cooking=True
BaoliPlayerCameraManager_C.PlayerCameraManager=True
BaoliPlayerCameraManager_C.CameraModifier=True
BaoliPlayerCameraManager_C.Debug=True
BaoliPlayerCameraManager_C.Collision=True
BaoliPlayerCameraManager_C.Actor=True
BaoliPlayerCameraManager_C.Input=True
BaoliPlayerCameraManager_C.HLOD=True
BaoliPlayerCameraManager_C.Events=True
CR_AO_C.Default=True
ControlRigWrapperObject_FRigControlElement.General=True
ControlRigWrapperObject_FRigControlElement.Control=True
ControlRigWrapperObject_FRigControlElement.Transform=True
ControlRigWrapperObject_FRigControlElement.Shape=True
ControlRigWrapperObject_-592631711.Nodes=True
SkeletalMesh.Material Slots=True
SkeletalMesh.LODCustomMode=True
SkeletalMesh.LOD0=True
SkeletalMesh.LODSettings=True
SkeletalMesh.Clothing=True
SkeletalMesh.SkeletalMesh=True
SkeletalMesh.Mesh=True
SkeletalMesh.SkinWeights=True
SkeletalMesh.ImportSettings=True
SkeletalMesh.AnimationRig=True
SkeletalMesh.Physics=True
SkeletalMesh.Lighting=True
SkeletalMesh.Animation=True
SkeletalMesh.RayTracing=True
SkeletalMesh.Sampling=True
SkeletalMesh.Deformer=True
SkeletalMesh.Rendering=True
SkeletalMeshClothBuildParams.Basic=True
SkeletalMeshClothBuildParams.Collision=True
AnimGraphNode_IdentityPose.Settings=True
AnimGraphNode_IdentityPose.Functions=True
AnimGraphNode_IdentityPose.Tag=True
AnimGraphNode_IdentityPose.Bindings=True
ControlRigWrapperObject_-1410011600.Nodes=True
ControlRigWrapperObject_582848483.Nodes=True
ControlRigWrapperObject_-619337892.Nodes=True
ControlRigWrapperObject_-501527475.Nodes=True
ControlRigWrapperObject_-1314496310.Nodes=True
ControlRigWrapperObject_1533679343.Nodes=True
AnimGraphNode_LocalToComponentSpace.Functions=True
AnimGraphNode_LocalToComponentSpace.Tag=True
AnimGraphNode_LocalToComponentSpace.Bindings=True
AnimGraphNode_SaveCachedPose.GraphNodeDetail=True
AnimGraphNode_SaveCachedPose.CachedPose=True
AnimGraphNode_SaveCachedPose.Functions=True
AnimGraphNode_SaveCachedPose.Tag=True
AnimGraphNode_SaveCachedPose.Bindings=True
AnimGraphNode_UseCachedPose.Functions=True
AnimGraphNode_UseCachedPose.Tag=True
AnimGraphNode_UseCachedPose.Bindings=True
BP_Bed_C.TransformCommon=True
BP_Bed_C.Construction=True
BP_Bed_C.AnimInstance=True
BP_Bed_C.Refrences=True
BP_Bed_C.References=True
BP_Bed_C.Montages=True
BP_Bed_C.Rendering=True
BP_Bed_C.Replication=True
BP_Bed_C.Collision=True
BP_Bed_C.HLOD=True
BP_Bed_C.Physics=True
BP_Bed_C.Networking=True
BP_Bed_C.Input=True
BP_Bed_C.Actor=True
ControlRigWrapperObject_1523111612.Nodes=True
ControlRigWrapperObject_1516794512.Nodes=True
ControlRigWrapperObject_-2029411952.Nodes=True
ControlRigWrapperObject_-1676131553.Nodes=True
ControlRigWrapperObject_-1356994462.Nodes=True
K2Node_MakeStruct.PinOptions=True
CapsuleComponent.Variable=True
CapsuleComponent.TransformCommon=True
CapsuleComponent.Sockets=True
CapsuleComponent.Shape=True
CapsuleComponent.Navigation=True
CapsuleComponent.HLOD=True
CapsuleComponent.ComponentTick=True
CapsuleComponent.Rendering=True
CapsuleComponent.Physics=True
CapsuleComponent.Collision=True
CapsuleComponent.Tags=True
CapsuleComponent.ComponentReplication=True
CapsuleComponent.Cooking=True
CapsuleComponent.Events=True
PropertyWrapper.Events=True
BP_ChestActorPawn_C.TransformCommon=True
BP_ChestActorPawn_C.Default=True
BP_ChestActorPawn_C.Construction=True
BP_ChestActorPawn_C.Pawn=True
BP_ChestActorPawn_C.Camera=True
BP_ChestActorPawn_C.Rendering=True
BP_ChestActorPawn_C.Replication=True
BP_ChestActorPawn_C.Collision=True
BP_ChestActorPawn_C.HLOD=True
BP_ChestActorPawn_C.Physics=True
BP_ChestActorPawn_C.Networking=True
BP_ChestActorPawn_C.Input=True
BP_ChestActorPawn_C.Actor=True
BP_ChestActorPawn_C.Tick=True
BP_ChestActorPawn_C.Events=True
CharacterMovementComponent.Variable=True
CharacterMovementComponent.Sockets=True
CharacterMovementComponent.Character Movement (General Settings)=True
CharacterMovementComponent.Character Movement: Walking=True
CharacterMovementComponent.Character Movement: Jumping / Falling=True
CharacterMovementComponent.ComponentTick=True
CharacterMovementComponent.Character Movement: Gravity=True
CharacterMovementComponent.Character Movement (Networking)=True
CharacterMovementComponent.Character Movement: Swimming=True
CharacterMovementComponent.Character Movement: Flying=True
CharacterMovementComponent.Character Movement: Custom Movement=True
CharacterMovementComponent.Character Movement (Rotation Settings)=True
CharacterMovementComponent.Character Movement: Physics Interaction=True
CharacterMovementComponent.Character Movement=True
CharacterMovementComponent.Character Movement: Avoidance=True
CharacterMovementComponent.RootMotion=True
CharacterMovementComponent.Character Movement: NavMesh Movement=True
CharacterMovementComponent.NavMovement=True
CharacterMovementComponent.Velocity=True
CharacterMovementComponent.PlanarMovement=True
CharacterMovementComponent.MovementComponent=True
CharacterMovementComponent.Tags=True
CharacterMovementComponent.ComponentReplication=True
CharacterMovementComponent.Activation=True
CharacterMovementComponent.Cooking=True
CharacterMovementComponent.Events=True
K2Node_CustomEvent.GraphNodeDetail=True
K2Node_CustomEvent.Graph=True
K2Node_CustomEvent.Inputs=True
UserDefinedStruct.Structure=True
AnimSequence.Animation=True
AnimSequence.Compression=True
AnimSequence.AdditiveSettings=True
AnimSequence.RootMotion=True
AnimSequence.ImportSettings=True
AnimSequence.Transform=True
AnimSequence.Miscellaneous=True
AnimSequence.File Path=True
AnimSequence.CustomAttributes=True
AnimSequence.Animation Model=True
AnimSequence.MetaData=True
AnimSequence.Thumbnail=True
BoneProxy.Bone=True
BoneProxy.Transforms=True
StaticMeshComponent.StaticMesh=True
StaticMeshComponent.TransformCommon=True
StaticMeshComponent.Physics=True
StaticMeshComponent.Collision=True
StaticMeshComponent.Lighting=True
StaticMeshComponent.Rendering=True
StaticMeshComponent.Mesh Painting=True
StaticMeshComponent.HLOD=True
StaticMeshComponent.Navigation=True
StaticMeshComponent.VirtualTexture=True
StaticMeshComponent.Tags=True
StaticMeshComponent.Cooking=True
StaticMeshComponent.Materials=True
AnimGraphNode_CallFunction.Warning=True
AnimGraphNode_CallFunction.Functions=True
AnimGraphNode_CallFunction.Function=True
AnimGraphNode_CallFunction.Tag=True
AnimGraphNode_CallFunction.Bindings=True
ControlRigWrapperObject_-1089398397.Nodes=True
AudioComponent.Variable=True
AudioComponent.TransformCommon=True
AudioComponent.Sockets=True
AudioComponent.Sound=True
AudioComponent.Parameters=True
AudioComponent.Attenuation=True
AudioComponent.Attachment=True
AudioComponent.Analysis=True
AudioComponent.Concurrency=True
AudioComponent.ComponentTick=True
AudioComponent.Subtitles=True
AudioComponent.Randomization=True
AudioComponent.Tags=True
AudioComponent.ComponentReplication=True
AudioComponent.Activation=True
AudioComponent.Cooking=True
AudioComponent.Events=True
SkeletalMeshSocket.Socket Parameters=True
ControlRigWrapperObject_2119422207.Nodes=True
ControlRigWrapperObject_2089149577.Nodes=True
ControlRigWrapperObject_875658551.Nodes=True
AnimGraphNode_OffsetRootBone.Warning=True
AnimGraphNode_OffsetRootBone.Functions=True
AnimGraphNode_OffsetRootBone.Evaluation=True
AnimGraphNode_OffsetRootBone.Settings=True
AnimGraphNode_OffsetRootBone.Tag=True
AnimGraphNode_OffsetRootBone.CollisionTesting=True
AnimGraphNode_OffsetRootBone.Bindings=True
AnimGraphNode_ApplyAdditive.Functions=True
AnimGraphNode_ApplyAdditive.Alpha=True
AnimGraphNode_ApplyAdditive.Performance=True
AnimGraphNode_ApplyAdditive.Tag=True
AnimGraphNode_ApplyAdditive.Bindings=True
AnimGraphNode_LayeredBoneBlend.Functions=True
AnimGraphNode_LayeredBoneBlend.Config=True
AnimGraphNode_LayeredBoneBlend.Tag=True
AnimGraphNode_LayeredBoneBlend.Runtime=True
AnimGraphNode_LayeredBoneBlend.Performance=True
AnimGraphNode_LayeredBoneBlend.Bindings=True
AnimGraphNode_TwoWayBlend.Functions=True
AnimGraphNode_TwoWayBlend.Settings=True
AnimGraphNode_TwoWayBlend.Option=True
AnimGraphNode_TwoWayBlend.Tag=True
AnimGraphNode_TwoWayBlend.Bindings=True
AnimGraphNode_BlendStack.Settings=True
AnimGraphNode_BlendStack.Functions=True
AnimGraphNode_BlendStack.Blendspace=True
AnimGraphNode_BlendStack.Sync=True
AnimGraphNode_BlendStack.Tag=True
AnimGraphNode_BlendStack.Relevancy=True
AnimGraphNode_BlendStack.Stitch.Experimental=True
AnimGraphNode_BlendStack.Bindings=True
FbxExportOption.Exporter=True
FbxExportOption.Mesh=True
FbxExportOption.StaticMesh=True
FbxExportOption.SkeletalMesh=True
FbxExportOption.Animation=True
FbxExportOption.Material=True
AnimGraphNode_BlendSpaceGraph.GraphNodeDetail=True
AnimGraphNode_BlendSpaceGraph.Blend Space=True
AnimGraphNode_BlendSpaceGraph.Functions=True
AnimGraphNode_BlendSpaceGraph.Coordinates=True
AnimGraphNode_BlendSpaceGraph.Sync=True
AnimGraphNode_BlendSpaceGraph.Tag=True
AnimGraphNode_BlendSpaceGraph.Bindings=True
BlendSpaceGraph.Axis Settings=True
BlendSpaceGraph.InputInterpolation=True
BlendSpaceGraph.SampleSmoothing=True
BlendSpaceGraph.BlendSamples=True
EulerAnalysisProperties.AnalysisProperties=True
REINST_CR_HeadManager_C_8.Default=True
K2Node_SetFieldsInStruct.PinOptions=True
AnimGraphNode_BlendSpaceEvaluator.Settings=True
AnimGraphNode_BlendSpaceEvaluator.Functions=True
AnimGraphNode_BlendSpaceEvaluator.Sync=True
AnimGraphNode_BlendSpaceEvaluator.Relevancy=True
AnimGraphNode_BlendSpaceEvaluator.Tag=True
AnimGraphNode_BlendSpaceEvaluator.Coordinates=True
AnimGraphNode_BlendSpaceEvaluator.Bindings=True
ControlRigWrapperObject_786182937.Nodes=True
ControlRigWrapperObject_289404103.Nodes=True
ControlRigWrapperObject_-1021361431.Nodes=True
ControlRigWrapperObject_1547712147.Nodes=True
ControlRigWrapperObject_-674762563.Nodes=True
ControlRigWrapperObject_-1714465748.Nodes=True
ControlRigGraph.Graph=True
AnimGraphNode_BlendListByInt.Functions=True
AnimGraphNode_BlendListByInt.Runtime=True
AnimGraphNode_BlendListByInt.Tag=True
AnimGraphNode_BlendListByInt.Config=True
AnimGraphNode_BlendListByInt.BlendType=True
AnimGraphNode_BlendListByInt.Option=True
AnimGraphNode_BlendListByInt.Bindings=True
ControlRigWrapperObject_1164909996.Nodes=True
ControlRigWrapperObject_-697724807.Nodes=True
ControlRigWrapperObject_-1723610182.Nodes=True
ControlRigWrapperObject_331962474.Nodes=True
REINST_CR_AO_C_12.Default=True
REINST_CR_HeadManager_C_14.Default=True
ControlRigWrapperObject_-342214179.Nodes=True
CameraComponent.Navigation=True

[AssetEditorToolkitTabLocation]
/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.BS_Neutral_AO_Stand=0
/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/Databases/Dense/PSD_Dense_Stand_Idles.PSD_Dense_Stand_Idles=1
/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases.CHT_PoseSearchDatabases=0
/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.CHT_PoseSearchDatabases_Dense=1
/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Sparse.CHT_PoseSearchDatabases_Sparse=0
/Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/Databases/Dense/PSD_Dense_Stand_TurnInPlace.PSD_Dense_Stand_TurnInPlace=0
/Game/Input/IA_phuphu.IA_phuphu=0
/Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter=0
/Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager=0
/Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter=0
/Game/Characters/Animations/CasRetarget/Cas_Skeleton.Cas_Skeleton=0
/Game/Input/IMC_GDCMotionMatching.IMC_GDCMotionMatching=0
/Game/Input/IA_Look.IA_Look=0
/Game/Blueprints/BP_BaoliController.BP_BaoliController=0
/Game/Blueprints/BaoliPlayerCameraManager.BaoliPlayerCameraManager=0
/Game/Characters/BaoliMC/CR_AO.CR_AO=0
/Game/Blueprints/BP_ChestActorPawn.BP_ChestActorPawn=1
/Game/Characters/UEFN_Mannequin/Animations/Idle/M_Neutral_Stand_Idle_Loop.M_Neutral_Stand_Idle_Loop=1
/Game/Characters/UEFN_Mannequin/Animations/Idle/M_Neutral_Stand_Idle_Break_v06.M_Neutral_Stand_Idle_Break_v06=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_LL_Lfoot.M_Neutral_Run_Box_B_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_LL_Rfoot.M_Neutral_Run_Box_B_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_LR_Lfoot.M_Neutral_Run_Box_B_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_LR_Rfoot.M_Neutral_Run_Box_B_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_RL_Lfoot.M_Neutral_Run_Box_B_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_RL_Rfoot.M_Neutral_Run_Box_B_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_RR_Lfoot.M_Neutral_Run_Box_B_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_B_RR_Rfoot.M_Neutral_Run_Box_B_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_LL_Lfoot.M_Neutral_Run_Box_F_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_LL_Rfoot.M_Neutral_Run_Box_F_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_LR_Lfoot.M_Neutral_Run_Box_F_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_LR_Rfoot.M_Neutral_Run_Box_F_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_RL_Lfoot.M_Neutral_Run_Box_F_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_RL_Rfoot.M_Neutral_Run_Box_F_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_RR_Lfoot.M_Neutral_Run_Box_F_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_F_RR_Rfoot.M_Neutral_Run_Box_F_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LL_B_Lfoot.M_Neutral_Run_Box_LL_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LL_B_Rfoot.M_Neutral_Run_Box_LL_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LL_F_Lfoot.M_Neutral_Run_Box_LL_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LL_F_Rfoot.M_Neutral_Run_Box_LL_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LR_B_Lfoot.M_Neutral_Run_Box_LR_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LR_B_Rfoot.M_Neutral_Run_Box_LR_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LR_F_Lfoot.M_Neutral_Run_Box_LR_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_LR_F_Rfoot.M_Neutral_Run_Box_LR_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RL_B_Lfoot.M_Neutral_Run_Box_RL_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RL_B_Rfoot.M_Neutral_Run_Box_RL_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RL_F_Lfoot.M_Neutral_Run_Box_RL_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RL_F_Rfoot.M_Neutral_Run_Box_RL_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RR_B_Lfoot.M_Neutral_Run_Box_RR_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RR_B_Rfoot.M_Neutral_Run_Box_RR_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RR_F_Lfoot.M_Neutral_Run_Box_RR_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Box_RR_F_Rfoot.M_Neutral_Run_Box_RR_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_B_FL_Lfoot.M_Neutral_Run_Diamond_B_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_B_FL_Rfoot.M_Neutral_Run_Diamond_B_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_B_FR_Lfoot.M_Neutral_Run_Diamond_B_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_B_FR_Rfoot.M_Neutral_Run_Diamond_B_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BL_BR_Lfoot.M_Neutral_Run_Diamond_BL_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BL_BR_Rfoot.M_Neutral_Run_Diamond_BL_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BL_F_Lfoot.M_Neutral_Run_Diamond_BL_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BL_F_Rfoot.M_Neutral_Run_Diamond_BL_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BL_FL_Lfoot.M_Neutral_Run_Diamond_BL_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BL_FL_Rfoot.M_Neutral_Run_Diamond_BL_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BR_BL_Lfoot.M_Neutral_Run_Diamond_BR_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BR_BL_Rfoot.M_Neutral_Run_Diamond_BR_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BR_F_Lfoot.M_Neutral_Run_Diamond_BR_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BR_F_Rfoot.M_Neutral_Run_Diamond_BR_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BR_FR_Lfoot.M_Neutral_Run_Diamond_BR_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_BR_FR_Rfoot.M_Neutral_Run_Diamond_BR_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_F_BL_Lfoot.M_Neutral_Run_Diamond_F_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_F_BL_Rfoot.M_Neutral_Run_Diamond_F_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_F_BR_Lfoot.M_Neutral_Run_Diamond_F_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_F_BR_Rfoot.M_Neutral_Run_Diamond_F_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FL_B_Lfoot.M_Neutral_Run_Diamond_FL_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FL_B_Rfoot.M_Neutral_Run_Diamond_FL_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FL_BL_Lfoot.M_Neutral_Run_Diamond_FL_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FL_BL_Rfoot.M_Neutral_Run_Diamond_FL_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FL_FR_Lfoot.M_Neutral_Run_Diamond_FL_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FL_FR_Rfoot.M_Neutral_Run_Diamond_FL_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FR_B_Lfoot.M_Neutral_Run_Diamond_FR_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FR_B_Rfoot.M_Neutral_Run_Diamond_FR_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FR_BR_Lfoot.M_Neutral_Run_Diamond_FR_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FR_BR_Rfoot.M_Neutral_Run_Diamond_FR_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FR_FL_Lfoot.M_Neutral_Run_Diamond_FR_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Diamond_FR_FL_Rfoot.M_Neutral_Run_Diamond_FR_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BL_RL_Lfoot.M_Neutral_Run_Hourglass_BL_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BL_RL_Rfoot.M_Neutral_Run_Hourglass_BL_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BL_RR_Lfoot.M_Neutral_Run_Hourglass_BL_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BL_RR_Rfoot.M_Neutral_Run_Hourglass_BL_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BR_LL_Lfoot.M_Neutral_Run_Hourglass_BR_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BR_LL_Rfoot.M_Neutral_Run_Hourglass_BR_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BR_LR_Lfoot.M_Neutral_Run_Hourglass_BR_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_BR_LR_Rfoot.M_Neutral_Run_Hourglass_BR_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FL_RL_Lfoot.M_Neutral_Run_Hourglass_FL_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FL_RL_Rfoot.M_Neutral_Run_Hourglass_FL_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FL_RR_Lfoot.M_Neutral_Run_Hourglass_FL_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FL_RR_Rfoot.M_Neutral_Run_Hourglass_FL_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FR_LL_Lfoot.M_Neutral_Run_Hourglass_FR_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FR_LL_Rfoot.M_Neutral_Run_Hourglass_FR_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FR_LR_Lfoot.M_Neutral_Run_Hourglass_FR_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_FR_LR_Rfoot.M_Neutral_Run_Hourglass_FR_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LL_BR_Lfoot.M_Neutral_Run_Hourglass_LL_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LL_BR_Rfoot.M_Neutral_Run_Hourglass_LL_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LL_FR_Lfoot.M_Neutral_Run_Hourglass_LL_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LL_FR_Rfoot.M_Neutral_Run_Hourglass_LL_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LR_BR_Lfoot.M_Neutral_Run_Hourglass_LR_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LR_BR_Rfoot.M_Neutral_Run_Hourglass_LR_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LR_FR_Lfoot.M_Neutral_Run_Hourglass_LR_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_LR_FR_Rfoot.M_Neutral_Run_Hourglass_LR_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RL_BL_Lfoot.M_Neutral_Run_Hourglass_RL_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RL_BL_Rfoot.M_Neutral_Run_Hourglass_RL_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RL_FL_Lfoot.M_Neutral_Run_Hourglass_RL_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RL_FL_Rfoot.M_Neutral_Run_Hourglass_RL_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RR_BL_Lfoot.M_Neutral_Run_Hourglass_RR_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RR_BL_Rfoot.M_Neutral_Run_Hourglass_RR_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RR_FL_Lfoot.M_Neutral_Run_Hourglass_RR_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Hourglass_RR_FL_Rfoot.M_Neutral_Run_Hourglass_RR_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Base.M_Neutral_Run_Lean_Pose_Base=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Left.M_Neutral_Run_Lean_Pose_Left=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Right.M_Neutral_Run_Lean_Pose_Right=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_B.M_Neutral_Run_Loop_B=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_BL.M_Neutral_Run_Loop_BL=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_BR.M_Neutral_Run_Loop_BR=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_F.M_Neutral_Run_Loop_F=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_F_L_20.M_Neutral_Run_Loop_F_L_20=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_F_R_20.M_Neutral_Run_Loop_F_R_20=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_FL.M_Neutral_Run_Loop_FL=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_FR.M_Neutral_Run_Loop_FR=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_LL.M_Neutral_Run_Loop_LL=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_LR_offset.M_Neutral_Run_Loop_LR_offset=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_RL.M_Neutral_Run_Loop_RL=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_RR_offset.M_Neutral_Run_Loop_RR_offset=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_B_F_Lfoot.M_Neutral_Run_Pivot_B_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_B_F_Rfoot.M_Neutral_Run_Pivot_B_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_BL_FR_Lfoot.M_Neutral_Run_Pivot_BL_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_BL_FR_Rfoot.M_Neutral_Run_Pivot_BL_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_BR_FL_Lfoot.M_Neutral_Run_Pivot_BR_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_BR_FL_Rfoot.M_Neutral_Run_Pivot_BR_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_F_B_Lfoot.M_Neutral_Run_Pivot_F_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_F_B_Rfoot.M_Neutral_Run_Pivot_F_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_FL_BR_Lfoot.M_Neutral_Run_Pivot_FL_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_FL_BR_Rfoot.M_Neutral_Run_Pivot_FL_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_FR_BL_Lfoot.M_Neutral_Run_Pivot_FR_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_FR_BL_Rfoot.M_Neutral_Run_Pivot_FR_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_LL_RL_Lfoot.M_Neutral_Run_Pivot_LL_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_LL_RL_Rfoot.M_Neutral_Run_Pivot_LL_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_LR_RR_Lfoot.M_Neutral_Run_Pivot_LR_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_LR_RR_Rfoot.M_Neutral_Run_Pivot_LR_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_RL_LL_Lfoot.M_Neutral_Run_Pivot_RL_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_RL_LL_Rfoot.M_Neutral_Run_Pivot_RL_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_RR_LR_Lfoot.M_Neutral_Run_Pivot_RR_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pivot_RR_LR_Rfoot.M_Neutral_Run_Pivot_RR_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_F_FL_Lfoot.M_Neutral_Run_Prism_F_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_F_FL_Rfoot.M_Neutral_Run_Prism_F_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_F_FR_Lfoot.M_Neutral_Run_Prism_F_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_F_FR_Rfoot.M_Neutral_Run_Prism_F_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_FL_F_Lfoot.M_Neutral_Run_Prism_FL_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_FL_F_Rfoot.M_Neutral_Run_Prism_FL_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_FR_F_Lfoot.M_Neutral_Run_Prism_FR_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Prism_FR_F_Rfoot.M_Neutral_Run_Prism_FR_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_B_L_090.M_Neutral_Run_Reface_Start_B_L_090=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_B_L_180.M_Neutral_Run_Reface_Start_B_L_180=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_B_R_090.M_Neutral_Run_Reface_Start_B_R_090=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_B_R_180.M_Neutral_Run_Reface_Start_B_R_180=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_F_L_090.M_Neutral_Run_Reface_Start_F_L_090=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_F_L_180.M_Neutral_Run_Reface_Start_F_L_180=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_F_R_090.M_Neutral_Run_Reface_Start_F_R_090=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Reface_Start_F_R_180.M_Neutral_Run_Reface_Start_F_R_180=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Shuffle_LR_to_LL_Lfoot.M_Neutral_Run_Shuffle_LR_to_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Shuffle_LR_to_LL_Rfoot.M_Neutral_Run_Shuffle_LR_to_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Shuffle_RR_to_RL_Lfoot.M_Neutral_Run_Shuffle_RR_to_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Shuffle_RR_to_RL_Rfoot.M_Neutral_Run_Shuffle_RR_to_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_B_Lfoot.M_Neutral_Run_Start_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_B_Rfoot.M_Neutral_Run_Start_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_BL_Lfoot.M_Neutral_Run_Start_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_BL_Rfoot.M_Neutral_Run_Start_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_BR_Lfoot.M_Neutral_Run_Start_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_BR_Rfoot.M_Neutral_Run_Start_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_F_Lfoot.M_Neutral_Run_Start_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_F_Rfoot.M_Neutral_Run_Start_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_FL_Lfoot.M_Neutral_Run_Start_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_FL_Rfoot.M_Neutral_Run_Start_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_FR_Lfoot.M_Neutral_Run_Start_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_FR_Rfoot.M_Neutral_Run_Start_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_LL_Lfoot.M_Neutral_Run_Start_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_LL_Rfoot.M_Neutral_Run_Start_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_LR_Lfoot.M_Neutral_Run_Start_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_LR_Rfoot.M_Neutral_Run_Start_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_RL_Lfoot.M_Neutral_Run_Start_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_RL_Rfoot.M_Neutral_Run_Start_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_RR_Lfoot.M_Neutral_Run_Start_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Start_RR_Rfoot.M_Neutral_Run_Start_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_B_Lfoot.M_Neutral_Run_Stop_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_B_Rfoot.M_Neutral_Run_Stop_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_BL_Lfoot.M_Neutral_Run_Stop_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_BL_Rfoot.M_Neutral_Run_Stop_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_BR_Lfoot.M_Neutral_Run_Stop_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_BR_Rfoot.M_Neutral_Run_Stop_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_F_Lfoot.M_Neutral_Run_Stop_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_F_Rfoot.M_Neutral_Run_Stop_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_FL_Lfoot.M_Neutral_Run_Stop_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_FL_Rfoot.M_Neutral_Run_Stop_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_FR_Lfoot.M_Neutral_Run_Stop_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_FR_Rfoot.M_Neutral_Run_Stop_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_LL_Lfoot.M_Neutral_Run_Stop_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_LL_Rfoot.M_Neutral_Run_Stop_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_LR_Lfoot.M_Neutral_Run_Stop_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_LR_Rfoot.M_Neutral_Run_Stop_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_RL_Lfoot.M_Neutral_Run_Stop_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_RL_Rfoot.M_Neutral_Run_Stop_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_RR_Lfoot.M_Neutral_Run_Stop_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Stop_RR_Rfoot.M_Neutral_Run_Stop_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_090_Lfoot.M_Neutral_Run_Turn_L_090_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_090_Rfoot.M_Neutral_Run_Turn_L_090_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_135_Lfoot.M_Neutral_Run_Turn_L_135_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_135_Rfoot.M_Neutral_Run_Turn_L_135_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_180_Lfoot.M_Neutral_Run_Turn_L_180_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_180_Rfoot.M_Neutral_Run_Turn_L_180_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_090_Lfoot.M_Neutral_Run_Turn_R_090_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_090_Rfoot.M_Neutral_Run_Turn_R_090_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_135_Lfoot.M_Neutral_Run_Turn_R_135_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_135_Rfoot.M_Neutral_Run_Turn_R_135_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_180_Lfoot.M_Neutral_Run_Turn_R_180_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_180_Rfoot.M_Neutral_Run_Turn_R_180_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Transition_Walk_to_Run_Lfoot.M_Neutral_Transition_Walk_to_Run_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Transition_Walk_to_Run_Rfoot.M_Neutral_Transition_Walk_to_Run_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Arc_Small_L.M_Neutral_Run_Arc_Small_L=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Arc_Small_R.M_Neutral_Run_Arc_Small_R=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Arc_Tight_L.M_Neutral_Run_Arc_Tight_L=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Arc_Tight_R.M_Neutral_Run_Arc_Tight_R=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Arc_Wide_L.M_Neutral_Run_Arc_Wide_L=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Arc_Wide_R.M_Neutral_Run_Arc_Wide_R=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Circle_Strafe_L.M_Neutral_Run_Circle_Strafe_L=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Circle_Strafe_R.M_Neutral_Run_Circle_Strafe_R=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Left_into.M_Neutral_Run_Lean_Pose_Left_into=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Left_out.M_Neutral_Run_Lean_Pose_Left_out=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Right_into.M_Neutral_Run_Lean_Pose_Right_into=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Lean_Pose_Right_out.M_Neutral_Run_Lean_Pose_Right_out=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_LR.M_Neutral_Run_Loop_LR=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_RR.M_Neutral_Run_Loop_RR=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_Strafe_BR.M_Neutral_Run_Loop_Strafe_BR=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Loop_Strafe_FL.M_Neutral_Run_Loop_Strafe_FL=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Pose_F_hold.M_Neutral_Run_Pose_F_hold=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Spin_F_B_Lfoot.M_Neutral_Run_Spin_F_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_045_Lfoot.M_Neutral_Run_Turn_L_045_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_L_045_Rfoot.M_Neutral_Run_Turn_L_045_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_045_Lfoot.M_Neutral_Run_Turn_R_045_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Run/M_Neutral_Run_Turn_R_045_Rfoot.M_Neutral_Run_Turn_R_045_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Transition_Run_to_Walk_Lfoot.M_Neutral_Transition_Run_to_Walk_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Transition_Run_to_Walk_Rfoot.M_Neutral_Transition_Run_to_Walk_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_B.M_Neutral_Walk_Loop_B=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_BL.M_Neutral_Walk_Loop_BL=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_BR.M_Neutral_Walk_Loop_BR=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_F.M_Neutral_Walk_Loop_F=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_FL.M_Neutral_Walk_Loop_FL=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_FR.M_Neutral_Walk_Loop_FR=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_LL.M_Neutral_Walk_Loop_LL=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_LR.M_Neutral_Walk_Loop_LR=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_RL.M_Neutral_Walk_Loop_RL=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_RR.M_Neutral_Walk_Loop_RR=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_B_F_Lfoot.M_Neutral_Walk_Pivot_B_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_B_F_Rfoot.M_Neutral_Walk_Pivot_B_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_BL_FR_Lfoot.M_Neutral_Walk_Pivot_BL_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_BL_FR_Rfoot.M_Neutral_Walk_Pivot_BL_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_BR_FL_Lfoot.M_Neutral_Walk_Pivot_BR_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_BR_FL_Rfoot.M_Neutral_Walk_Pivot_BR_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_F_B_Lfoot.M_Neutral_Walk_Pivot_F_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_F_B_Rfoot.M_Neutral_Walk_Pivot_F_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_FL_BR_Lfoot.M_Neutral_Walk_Pivot_FL_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_FL_BR_Rfoot.M_Neutral_Walk_Pivot_FL_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_FR_BL_Lfoot.M_Neutral_Walk_Pivot_FR_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_FR_BL_Rfoot.M_Neutral_Walk_Pivot_FR_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_LL_RL_Lfoot.M_Neutral_Walk_Pivot_LL_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_LL_RL_Rfoot.M_Neutral_Walk_Pivot_LL_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_LR_RR_Lfoot.M_Neutral_Walk_Pivot_LR_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_LR_RR_Rfoot.M_Neutral_Walk_Pivot_LR_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_RL_LL_Lfoot.M_Neutral_Walk_Pivot_RL_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_RL_LL_Rfoot.M_Neutral_Walk_Pivot_RL_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_RR_LR_Lfoot.M_Neutral_Walk_Pivot_RR_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Pivot_RR_LR_Rfoot.M_Neutral_Walk_Pivot_RR_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_B_L_090.M_Neutral_Walk_Reface_Start_B_L_090=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_B_L_180.M_Neutral_Walk_Reface_Start_B_L_180=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_B_R_090.M_Neutral_Walk_Reface_Start_B_R_090=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_B_R_180.M_Neutral_Walk_Reface_Start_B_R_180=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_F_L_090.M_Neutral_Walk_Reface_Start_F_L_090=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_F_L_180.M_Neutral_Walk_Reface_Start_F_L_180=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_F_R_090.M_Neutral_Walk_Reface_Start_F_R_090=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Reface_Start_F_R_180.M_Neutral_Walk_Reface_Start_F_R_180=0
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_B_Lfoot.M_Neutral_Walk_Start_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_B_Rfoot.M_Neutral_Walk_Start_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_BL_Lfoot.M_Neutral_Walk_Start_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_BL_Rfoot.M_Neutral_Walk_Start_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_BR_Lfoot.M_Neutral_Walk_Start_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_BR_Rfoot.M_Neutral_Walk_Start_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_F_Lfoot.M_Neutral_Walk_Start_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_F_Rfoot.M_Neutral_Walk_Start_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_FL_Lfoot.M_Neutral_Walk_Start_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_FL_Rfoot.M_Neutral_Walk_Start_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_FR_Lfoot.M_Neutral_Walk_Start_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_FR_Rfoot.M_Neutral_Walk_Start_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_LL_Lfoot.M_Neutral_Walk_Start_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_LL_Rfoot.M_Neutral_Walk_Start_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_LR_Lfoot.M_Neutral_Walk_Start_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_LR_Rfoot.M_Neutral_Walk_Start_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_RL_Lfoot.M_Neutral_Walk_Start_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_RL_Rfoot.M_Neutral_Walk_Start_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_RR_Lfoot.M_Neutral_Walk_Start_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Start_RR_Rfoot.M_Neutral_Walk_Start_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_B_Lfoot.M_Neutral_Walk_Stop_B_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_B_Rfoot.M_Neutral_Walk_Stop_B_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_BL_Lfoot.M_Neutral_Walk_Stop_BL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_BL_Rfoot.M_Neutral_Walk_Stop_BL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_BR_Lfoot.M_Neutral_Walk_Stop_BR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_BR_Rfoot.M_Neutral_Walk_Stop_BR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_F_Lfoot.M_Neutral_Walk_Stop_F_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_F_Rfoot.M_Neutral_Walk_Stop_F_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_FL_Lfoot.M_Neutral_Walk_Stop_FL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_FL_Rfoot.M_Neutral_Walk_Stop_FL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_FR_Lfoot.M_Neutral_Walk_Stop_FR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_FR_Rfoot.M_Neutral_Walk_Stop_FR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_LL_Lfoot.M_Neutral_Walk_Stop_LL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_LL_Rfoot.M_Neutral_Walk_Stop_LL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_LR_Lfoot.M_Neutral_Walk_Stop_LR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_LR_Rfoot.M_Neutral_Walk_Stop_LR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_RL_Lfoot.M_Neutral_Walk_Stop_RL_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_RL_Rfoot.M_Neutral_Walk_Stop_RL_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_RR_Lfoot.M_Neutral_Walk_Stop_RR_Lfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Stop_RR_Rfoot.M_Neutral_Walk_Stop_RR_Rfoot=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Arc_F_Small_L.M_Neutral_Walk_Arc_F_Small_L=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Arc_F_Small_R.M_Neutral_Walk_Arc_F_Small_R=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Arc_F_Tight_L.M_Neutral_Walk_Arc_F_Tight_L=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Arc_F_Tight_R.M_Neutral_Walk_Arc_F_Tight_R=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Arc_F_Wide_L.M_Neutral_Walk_Arc_F_Wide_L=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Arc_F_Wide_R.M_Neutral_Walk_Arc_F_Wide_R=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Circle_Strafe_L.M_Neutral_Walk_Circle_Strafe_L=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Circle_Strafe_R.M_Neutral_Walk_Circle_Strafe_R=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_F_L_20.M_Neutral_Walk_Loop_F_L_20=1
/Game/Characters/UEFN_Mannequin/Animations/Walk/M_Neutral_Walk_Loop_F_R_20.M_Neutral_Walk_Loop_F_R_20=1
/Game/Characters/BaoliMC/JoinHuman.JoinHuman=0
/Game/MetaHumans/Common/Female/Medium/NormalWeight/Body/metahuman_base_skel.metahuman_base_skel=0
/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_None.BS_Neutral_AO_None=0
/Game/Characters/UEFN_Mannequin/Meshes/SK_UEFN_Mannequin.SK_UEFN_Mannequin=1
/Game/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+135_Y0.M_Neutral_AO_Stand_X+135_Y0=1
/Game/Blueprints/BP_BaoliCharacter_DEADPACKAGE_1.BP_BaoliCharacter=0

[/Script/PinnedCommandList.PinnedCommandListSettings]
Contexts=(Name="AnimationEditor",Commands=)
Contexts=(Name="AnimationEditor.Viewport0",Commands=)
Contexts=(Name="AnimationBlueprintEditor.Viewport0",Commands=)
Contexts=(Name="AnimationBlueprintEditor",Commands=)
Contexts=(Name="None0",Commands=)
Contexts=(Name="SkeletonEditor.Viewport0",Commands=)
Contexts=(Name="SkeletonEditor",Commands=)
Contexts=(Name="SkeletalMeshEditor.Viewport0",Commands=)
Contexts=(Name="SkeletalMeshEditor",Commands=)

[DetailCategoriesAdvanced]
InputAction.Action=True
CameraComponent.CameraOptions=True
SpringArmComponent.Lag=True
K2Node_FunctionEntry.Graph=False
K2Node_VariableGet.Variable=False

[/Script/UnrealEd.AnimationBlueprintEditorOptions]
bHideUnrelatedNodes=False

[/Script/BlueprintGraph.BlueprintEditorSettings]
bDrawMidpointArrowsInBlueprints=False
bShowGraphInstructionText=True
bHideUnrelatedNodes=False
bShowShortTooltips=True
bShowFunctionParameterIcon=True
bShowFunctionLocalVariableIcon=True
bEnableInputTriggerSupportWarnings=False
bSplitContextTargetSettings=True
bExposeAllMemberComponentFunctions=True
bShowContextualFavorites=False
bExposeDeprecatedFunctions=False
bCompactCallOnMemberNodes=False
bFlattenFavoritesMenus=True
bAutoCastObjectConnections=False
bShowViewportOnSimulate=False
bSpawnDefaultBlueprintNodes=True
bHideConstructionScriptComponentsInDetailsView=True
bHostFindInBlueprintsInGlobalTab=True
bNavigateToNativeFunctionsFromCallNodes=True
bDoubleClickNavigatesToParent=True
bEnableTypePromotion=True
TypePromotionPinDenyList=string
TypePromotionPinDenyList=text
BreakpointReloadMethod=RestoreAll
bEnablePinValueInspectionTooltips=True
bEnableNamespaceEditorFeatures=True
bEnableContextMenuTimeSlicing=True
ContextMenuTimeSlicingThresholdMs=50
bIncludeActionsForSelectedAssetsInContextMenu=False
bLimitAssetActionBindingToSingleSelectionOnly=False
bLoadSelectedAssetsForContextMenuActionBinding=True
bDoNotMarkAllInstancesDirtyOnDefaultValueChange=True
bFavorPureCastNodes=False
SaveOnCompile=SoC_Never
bJumpToNodeErrors=False
bAllowExplicitImpureNodeDisabling=False
bShowActionMenuItemSignatures=False
bBlueprintNodeUniqueNames=False
NodeTemplateCacheCapMB=20.000000
AllowIndexAllBlueprints=LoadOnly
bShowInheritedVariables=False
bAlwaysShowInterfacesInOverrides=True
bShowParentClassInOverrides=True
bShowEmptySections=True
bShowAccessSpecifier=False
Bookmarks=()
PerBlueprintSettings=()
bIncludeCommentNodesInBookmarksTab=True
bShowBookmarksForCurrentDocumentOnlyInTab=False
GraphEditorQuickJumps=()

[SkeletalMeshModelingTools]
EditingModeActive=True

[PropertyWindowWidths]
PropertyEditorToolkit_PinAsColumnHeader=24
Property=0.5
Value=1

[FBX_Export_UI_Option_FbxExportOption]
FbxExportCompatibility=FBX_2013
bASCII=False
bForceFrontXAxis=False
VertexColor=True
LevelOfDetail=True
Collision=True
bExportSourceMesh=False
bExportMorphTargets=True
bExportPreviewMesh=True
MapSkeletalMotionToRoot=False
bExportLocalTime=True
BakeCameraAndLightAnimation=BakeTransforms
BakeActorAnimation=None
BakeMaterialInputs=Disabled
DefaultMaterialBakeSize=(Size=(X=1024,Y=1024),bAutoDetect=True)

[SuppressableDialogs]
DeleteConfirmationVariable_Warning=False

