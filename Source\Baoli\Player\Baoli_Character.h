// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Camera/CameraComponent.h"
#include "Components/PointLightComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/SpringArmComponent.h"
#include "Kismet/GameplayStatics.h"
#include "EnhancedInputSubsystems.h"
#include "MotionWarpingComponent.h"
#include "Components/SpotLightComponent.h"
#include "Baoli_Character.generated.h"


enum class ECharacterState : uint8;
class ABaoli_Controller;

//Gait Reference
UENUM(BlueprintType,Blueprintable)
enum EMyGait
	{
		Walk   UMETA(DisplayName = "Walk"),
		Run    UMETA(DisplayName = "Run"),
		Sprint UMETA(DisplayName = "Sprint")
	};
//Cover Reference
UENUM(BlueprintType,Blueprintable)
enum ECoverSystem
{
	None   UMETA(DisplayName = "None"),
	Almari UMETA(DisplayName = "<PERSON><PERSON>"),
	Bed UMETA(DisplayName = "Bed")
};

UCLASS()
class BAOLI_API ABaoli_Character : public ACharacter
{
	GENERATED_BODY()


	/////////////////////////////////////////////// Construction Section ///////////////////////////////////////////////
public:
	// Sets default values for this character's properties
	ABaoli_Character();
	

	//SpringArm
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	USpringArmComponent* SpringArmComponent;

	//Camera
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UCameraComponent* CameraComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Camera)
	float FOV = 90.0f;

	//Inspection Light
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UPointLightComponent* InspectionLightComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = InspectionLight)
	int InspectionLightIntensity = 350.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite,Category = InpsectionLight)
	int InspectionAttenuationRadius = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Components)
	UMotionWarpingComponent* MotionWarpingComponent;


	/////////////////////////////////////////////// Overriden Functions ////////////////////////////////////////////////
protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	// Called every frame
	virtual void Tick(float DeltaTime) override;

	/////////////////////////////////////////////////// Key Bindings ///////////////////////////////////////////////////
public:
	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	//Player Controller Reference
	UPROPERTY()
	TObjectPtr<ABaoli_Controller> PlayerController;

	//Input mapping Context
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Input)
	UInputMappingContext* InputMapping;

	void Move(const FVector2D MovementVector);

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input, meta=(AllowPrivateAccess = "true"))
	UInputAction* LookAction;

	void Look(const FInputActionValue& value);

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input,meta=(AllowPrivateAccess = "true"))
	UInputAction* WalkAction;

	void Walk();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input,meta=(AllowPrivateAccess = "true"))
	UInputAction* SprintAction;

	void Sprint(const FInputActionValue& Value);

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input,meta=(AllowPrivateAccess = "true"))
	UInputAction* StrafeAction;

	void Strafe();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input,meta=(AllowPrivateAccess = "true"))
	UInputAction* AimAction;

	void Aim();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input,meta=(AllowPrivateAccess = "true"))
	UInputAction* CrouchAction;

	void MyCrouch();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input,meta=(AllowPrivateAccess = "true"))
	UInputAction* JumpAction;

	void Jumping();

	///////////////////////////////////////////////////// Variables ////////////////////////////////////////////////////
public:
	//Movement Variables
	UPROPERTY(EditAnywhere, Category="Movement")
	bool bWantsToSprint = false;

	UPROPERTY(BlueprintReadOnly, Category="Movement")
	TEnumAsByte<EMyGait> CurrentGait;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	TEnumAsByte<ECoverSystem> CoverSystem = None;

	UPROPERTY()
	UCharacterMovementComponent* MovementComponent = GetCharacterMovement();

	UPROPERTY(EditAnywhere, Category="Movement")
	UCurveFloat* SpeedMapCurve;

	UPROPERTY(EditAnywhere, Category="Movement")
	bool bWantsToStrafe = true;

	//Aiming Variables
	UPROPERTY(EditAnywhere, Category="Sensitivity")
	float AimSensitivity = 0.5f;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category="Animation Variables")
	bool bWentInBed;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category="Animation Variables")
	bool bWentInAlmirah;

	//Flashlight Variables
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category="Flashlight Variables")
	bool bHasFlashlight;

private:

	const ECharacterState* CharacterState;

	// Movement Start Detection Variables
	bool bWasMovingLastFrame = false;
	bool bIsInMovementStartWindow = false;
	int32 MovementStartFrameCount = 0;
	int32 MovementStartWindowFrames = 50;

	///////////////////////////////////////////////////// Functions ////////////////////////////////////////////////////
public:

	//Movement Script
	void UpdateMovement();
	// Update Gait Per Tick
	EMyGait getDesiredGait();
	// Calculate Max Speed
	float CalcMaxSpeed();

	//Rotation Script
	void UpdateRotation();

	//Movement Start Detection Script
	void UpdateMovementStartDetection(float DeltaTime);

	UFUNCTION(BlueprintCallable)
	void DisableCharacter(bool bDisableInput, bool bDisableMesh, bool bDisableCursor, bool bAnimationCamera, bool bDisableSkeletonUpdate);

	UFUNCTION(BlueprintCallable)
	void EnableCharacter(bool bEnableInput, bool bEnableMesh, bool bEnableCursor, bool bPlayerCamera, bool bEnableSkeletonUpdate);

	// Movement Start Detection Function
	UFUNCTION(BlueprintPure, Category="Movement")
	bool IsInMovementStartWindow() const;

};
