﻿Log file open, 06/11/25 16:10:58
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=47056)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.819840
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-16BA37E040D069A3122815AFA27C6061
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogConfig: Display: Loading VulkanPC ini files took 0.03 seconds
LogConfig: Display: Loading Unix ini files took 0.03 seconds
LogConfig: Display: Loading Windows ini files took 0.03 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.07 seconds
LogConfig: Display: Loading VisionOS ini files took 0.09 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.10 seconds
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogAssetRegistry: Display: Asset registry cache read as 106.4 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin PlatformFunctions
LogPluginManager: Mounting Project plugin SnappingHelper
LogPluginManager: Mounting Project plugin rdBPtools
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.54ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.11-10.41.00:776][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.11-10.41.00:776][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.11-10.41.00:776][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.11-10.41.00:776][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.11-10.41.00:776][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.11-10.41.00:776][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.11-10.41.00:777][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.11-10.41.00:777][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.11-10.41.00:777][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.11-10.41.00:777][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.11-10.41.00:781][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.11-10.41.00:782][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.11-10.41.00:782][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.11-10.41.00:785][  0]LogRHI: Using Default RHI: D3D12
[2025.06.11-10.41.00:785][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.11-10.41.00:785][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.11-10.41.00:819][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.11-10.41.00:819][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.11-10.41.01:430][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.11-10.41.01:430][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.11-10.41.01:430][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.11-10.41.01:431][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.11-10.41.01:431][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.11-10.41.01:617][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.11-10.41.01:617][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.11-10.41.01:617][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.11-10.41.01:617][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.11-10.41.01:617][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.11-10.41.01:626][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.11-10.41.01:626][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.11-10.41.01:626][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.11-10.41.01:740][  0]LogD3D12RHI: Found D3D12 adapter 3: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.11-10.41.01:740][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.11-10.41.01:740][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.11-10.41.01:740][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.11-10.41.01:740][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.11-10.41.01:740][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.11-10.41.01:740][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.11-10.41.01:740][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.11-10.41.01:740][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.11-10.41.01:740][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.11-10.41.01:741][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.11-10.41.01:741][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.11-10.41.01:741][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.11-10.41.01:741][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.11-10.41.01:741][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.11-10.41.01:741][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.11-10.41.01:741][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.11-10.41.01:741][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.11-10.41.01:741][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.11-10.41.01:741][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.11-10.41.01:741][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.11-10.41.01:741][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.11-10.41.01:741][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.11-10.41.01:741][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.11-10.41.01:741][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.11-10.41.01:741][  0]LogInit: User: Shashank
[2025.06.11-10.41.01:741][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.11-10.41.01:741][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.11-10.41.02:562][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.11-10.41.02:562][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.11-10.41.02:562][  0]LogMemory: Process Physical Memory: 697.70 MB used, 751.73 MB peak
[2025.06.11-10.41.02:562][  0]LogMemory: Process Virtual Memory: 829.58 MB used, 829.58 MB peak
[2025.06.11-10.41.02:562][  0]LogMemory: Physical Memory: 31265.14 MB used,  34185.66 MB free, 65450.80 MB total
[2025.06.11-10.41.02:562][  0]LogMemory: Virtual Memory: 46305.18 MB used,  23241.62 MB free, 69546.80 MB total
[2025.06.11-10.41.02:562][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.11-10.41.02:568][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.11-10.41.02:625][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.11-10.41.02:625][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.11-10.41.02:626][  0]LogInit: Using OS detected language (en-GB).
[2025.06.11-10.41.02:626][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.11-10.41.02:655][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.11-10.41.02:655][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.11-10.41.03:118][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.11-10.41.03:118][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.11-10.41.03:118][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.11-10.41.03:209][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.11-10.41.03:209][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.11-10.41.03:488][  0]LogRHI: Using Default RHI: D3D12
[2025.06.11-10.41.03:488][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.11-10.41.03:488][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.11-10.41.03:488][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.11-10.41.03:488][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.11-10.41.03:488][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.11-10.41.03:488][  0]LogWindows: Attached monitors:
[2025.06.11-10.41.03:488][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY5' [PRIMARY]
[2025.06.11-10.41.03:488][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY6'
[2025.06.11-10.41.03:488][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY7'
[2025.06.11-10.41.03:488][  0]LogWindows: Found 3 attached monitors.
[2025.06.11-10.41.03:488][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.11-10.41.03:488][  0]LogRHI: RHI Adapter Info:
[2025.06.11-10.41.03:488][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.11-10.41.03:488][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.11-10.41.03:488][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.11-10.41.03:488][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.11-10.41.03:525][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.11-10.41.03:638][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.11-10.41.03:638][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.11-10.41.04:148][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.11-10.41.04:148][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: Raster order views are supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.11-10.41.04:149][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.11-10.41.04:175][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000678F9365300)
[2025.06.11-10.41.04:176][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000678F9365580)
[2025.06.11-10.41.04:176][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000678F9365800)
[2025.06.11-10.41.04:177][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.11-10.41.04:177][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.11-10.41.04:177][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.11-10.41.04:177][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.11-10.41.04:177][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.11-10.41.04:177][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.11-10.41.04:200][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.11-10.41.04:205][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.11-10.41.04:260][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.11-10.41.04:260][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.11-10.41.04:429][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.11-10.41.04:429][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.11-10.41.04:429][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.11-10.41.04:429][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.11-10.41.04:429][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.11-10.41.04:429][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.11-10.41.04:429][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.11-10.41.04:459][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.11-10.41.04:486][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.11-10.41.04:597][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.11-10.41.04:711][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.11-10.41.04:711][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.11-10.41.04:784][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.11-10.41.04:784][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.11-10.41.04:784][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.11-10.41.04:784][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.11-10.41.04:844][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.11-10.41.04:844][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.11-10.41.04:844][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.11-10.41.04:937][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.11-10.41.04:937][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.11-10.41.04:937][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.11-10.41.04:937][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.11-10.41.04:999][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.11-10.41.04:999][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.11-10.41.05:145][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.11-10.41.05:145][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.11-10.41.05:145][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.11-10.41.05:145][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.11-10.41.05:145][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.11-10.41.05:499][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.11-10.41.05:538][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.11-10.41.05:538][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.11-10.41.05:538][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.11-10.41.05:541][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.11-10.41.05:541][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.11-10.41.05:541][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.11-10.41.05:541][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.11-10.41.05:541][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.11-10.41.05:674][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.11-10.41.05:674][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.11-10.41.05:674][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.11-10.41.05:675][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.11-10.41.05:675][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.11-10.41.05:675][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.11-10.41.05:676][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.11-10.41.05:676][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 31320 --child-id Zen_31320_Startup'
[2025.06.11-10.41.05:764][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.11-10.41.05:764][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.089 seconds
[2025.06.11-10.41.05:765][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.11-10.41.05:773][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.11-10.41.05:773][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=211.55MBs, RandomWriteSpeed=321.84MBs. Assigned SpeedClass 'Local'
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.11-10.41.05:774][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.11-10.41.05:774][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/31320/).
[2025.06.11-10.41.05:774][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/BCD86D204CC9C243B899299C58C58E49/'.
[2025.06.11-10.41.05:774][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.11-10.41.05:774][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.11-10.41.05:775][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.11-10.41.05:775][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.11-10.41.09:733][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.11-10.42.23:276][  0]LogSlate: Using FreeType 2.10.0
[2025.06.11-10.42.23:312][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.11-10.42.23:313][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.11-10.42.23:313][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.11-10.42.23:484][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.11-10.42.23:484][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.11-10.42.23:484][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.11-10.42.23:484][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.11-10.42.23:707][  0]LogAssetRegistry: FAssetRegistry took 0.0021 seconds to start up
[2025.06.11-10.42.23:709][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.11-10.42.23:712][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.11-10.42.23:867][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.11-10.42.23:972][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.11-10.42.23:972][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.11-10.42.23:972][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.11-10.42.23:982][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.11-10.42.23:982][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.11-10.42.24:006][  0]LogDeviceProfileManager: Active device profile: [0000067912698A00][000006790A216000 66] WindowsEditor
[2025.06.11-10.42.24:006][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.11-10.42.24:006][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.11-10.42.24:031][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.11-10.42.24:031][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.11-10.42.24:782][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.11-10.42.24:834][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:834][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.11-10.42.24:834][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.11-10.42.24:835][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.11-10.42.24:836][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.11-10.42.24:837][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.11-10.42.24:838][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.11-10.42.24:839][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.11-10.42.24:839][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.11-10.42.24:839][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.24:839][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.11-10.42.24:839][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.11-10.42.25:297][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.11-10.42.25:297][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.11-10.42.25:297][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.11-10.42.25:399][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.11-10.42.25:399][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.11-10.42.25:399][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.11-10.42.25:399][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.11-10.42.25:399][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.11-10.42.25:866][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.11-10.42.25:901][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.11-10.42.25:913][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.11-10.42.25:914][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.11-10.42.26:403][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.11-10.42.26:404][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.11-10.42.26:427][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.11-10.42.26:427][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.11-10.42.26:427][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.11-10.42.26:431][  0]LogLiveCoding: Display: Waiting for server
[2025.06.11-10.42.26:561][  0]LogSlate: Border
[2025.06.11-10.42.26:561][  0]LogSlate: BreadcrumbButton
[2025.06.11-10.42.26:561][  0]LogSlate: Brushes.Title
[2025.06.11-10.42.26:561][  0]LogSlate: Default
[2025.06.11-10.42.26:561][  0]LogSlate: Icons.Save
[2025.06.11-10.42.26:561][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.11-10.42.26:561][  0]LogSlate: ListView
[2025.06.11-10.42.26:561][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.11-10.42.26:561][  0]LogSlate: SoftwareCursor_Grab
[2025.06.11-10.42.26:561][  0]LogSlate: TableView.DarkRow
[2025.06.11-10.42.26:561][  0]LogSlate: TableView.Row
[2025.06.11-10.42.26:561][  0]LogSlate: TreeView
[2025.06.11-10.42.29:991][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.11-10.42.30:390][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.11-10.42.30:392][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.676 ms
[2025.06.11-10.42.30:438][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.43ms
[2025.06.11-10.42.30:540][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.11-10.42.30:540][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.11-10.42.30:540][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.11-10.42.30:540][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.11-10.42.30:746][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 8D445B0850C045C18000000000002500 | Instance: B8C6C0D041367871A825BD9BAF9C0446 (DESKTOP-E41IK6R-31320).
[2025.06.11-10.42.33:122][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.11-10.42.33:137][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.11-10.42.33:137][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.11-10.42.33:137][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:57622'.
[2025.06.11-10.42.33:140][  0]LogUdpMessaging: Display: Added local interface '172.25.96.1' to multicast group '230.0.0.1:6666'
[2025.06.11-10.42.33:140][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '230.0.0.1:6666'
[2025.06.11-10.42.33:140][  0]LogUdpMessaging: Display: Added local interface '172.22.96.1' to multicast group '230.0.0.1:6666'
[2025.06.11-10.42.33:258][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.11-10.42.33:258][  0]LogNNERuntimeORT: 0: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.11-10.42.33:258][  0]LogNNERuntimeORT: 1: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.11-10.42.33:258][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.11-10.42.33:258][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.11-10.42.33:877][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.11-10.42.33:877][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.11-10.42.33:891][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.11-10.42.34:446][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.11-10.42.34:447][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.11-10.42.35:415][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.11-10.42.36:092][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.11-10.42.36:411][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.11-10.42.36:873][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.11-10.42.36:873][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.11-10.42.36:933][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.11-10.42.37:002][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.11-10.42.37:106][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.11-10.42.37:106][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.11-10.42.37:741][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.11-10.42.37:756][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.11-10.42.37:766][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.11-10.42.37:766][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.11-10.42.37:772][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.11-10.42.37:773][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.11-10.42.37:773][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.11-10.42.37:773][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.11-10.42.37:780][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.11-10.42.37:790][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.11-10.42.37:797][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.11-10.42.37:797][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.11-10.42.37:797][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.11-10.42.37:809][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.11-10.42.37:812][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.11-10.42.37:824][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.11-10.42.37:828][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.11-10.42.37:832][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.11-10.42.37:838][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.11-10.42.37:845][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.11-10.42.37:845][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.11-10.42.37:855][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.11-10.42.37:856][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.11-10.42.37:860][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.11-10.42.37:861][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.11-10.42.37:865][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.11-10.42.37:872][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.11-10.42.37:877][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.11-10.42.37:878][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.11-10.42.37:884][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.11-10.42.37:885][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.11-10.42.37:885][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.11-10.42.37:896][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.11-10.42.37:897][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.11-10.42.37:897][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.11-10.42.38:152][  0]LogTimingProfiler: Initialize
[2025.06.11-10.42.38:152][  0]LogTimingProfiler: OnSessionChanged
[2025.06.11-10.42.38:152][  0]LoadingProfiler: Initialize
[2025.06.11-10.42.38:152][  0]LoadingProfiler: OnSessionChanged
[2025.06.11-10.42.38:152][  0]LogNetworkingProfiler: Initialize
[2025.06.11-10.42.38:152][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.11-10.42.38:152][  0]LogMemoryProfiler: Initialize
[2025.06.11-10.42.38:152][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.11-10.42.39:209][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.11-10.42.39:260][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.43ms
[2025.06.11-10.42.41:561][  0]LogCollectionManager: Loaded 1 collections in 0.000665 seconds
[2025.06.11-10.42.41:563][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.11-10.42.41:566][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.11-10.42.41:568][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.11-10.42.41:780][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.52ms
[2025.06.11-10.42.41:782][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.11-10.42.41:782][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.11-10.42.41:784][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.11-10.42.41:784][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.11-10.42.41:784][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.11-10.42.41:784][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.11-10.42.41:811][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.11-10.42.41:811][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.11-10.42.41:918][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.11-10.42.41:918][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.11-10.42.41:920][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.11-10.42.41:920][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.11-10.42.41:920][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.11-10.42.41:920][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.11-10.42.41:945][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.11-10.42.41:945][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.11-10.42.41:969][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-11T10:42:41.969Z using C
[2025.06.11-10.42.41:970][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.11-10.42.41:970][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.11-10.42.41:971][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.11-10.42.41:975][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.11-10.42.41:975][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.11-10.42.41:976][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.11-10.42.41:976][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000046
[2025.06.11-10.42.41:976][  0]LogFab: Display: Logging in using persist
[2025.06.11-10.42.41:977][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.11-10.42.42:077][  0]LogUObjectArray: 47650 objects as part of root set at end of initial load.
[2025.06.11-10.42.42:077][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.11-10.42.42:089][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40253 public script object entries (1077.00 KB)
[2025.06.11-10.42.42:089][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.11-10.42.42:246][  0]LogEngine: Initializing Engine...
[2025.06.11-10.42.42:247][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.11-10.42.42:247][  0]InkPlusPlus: FStory::FStory 000006792E29C610
[2025.06.11-10.42.42:247][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.11-10.42.42:267][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.11-10.42.42:267][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.11-10.42.42:402][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.11-10.42.42:427][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.11-10.42.42:427][  0]LogGameFeatures: Loading 233 builtins
[2025.06.11-10.42.42:428][  0]LogGameFeatures: Display: Total built in plugin load time 0.0007s
[2025.06.11-10.42.42:428][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.11-10.42.42:428][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.11-10.42.42:458][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.11-10.42.42:458][  0]LogInit: Texture streaming: Enabled
[2025.06.11-10.42.42:511][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.11-10.42.42:524][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.11-10.42.42:565][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.11-10.42.42:565][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.11-10.42.42:565][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.11-10.42.42:565][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.11-10.42.42:565][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.11-10.42.42:565][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.11-10.42.42:565][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.11-10.42.42:566][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.11-10.42.42:566][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.11-10.42.42:585][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.11-10.42.42:643][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.11-10.42.42:643][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.11-10.42.42:645][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.11-10.42.42:645][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.11-10.42.42:646][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.11-10.42.42:646][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.11-10.42.42:648][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.11-10.42.42:648][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.11-10.42.42:648][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.11-10.42.42:648][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.11-10.42.42:648][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.11-10.42.42:662][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.11-10.42.42:665][  0]LogInit: Undo buffer set to 256 MB
[2025.06.11-10.42.42:665][  0]LogInit: Transaction tracking system initialized
[2025.06.11-10.42.43:093][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.54ms
[2025.06.11-10.42.43:094][  0]LocalizationService: Localization service is disabled
[2025.06.11-10.42.43:797][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.19s
[2025.06.11-10.42.43:932][  0]LogPython: Using Python 3.11.8
[2025.06.11-10.42.45:295][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.11-10.42.45:340][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.11-10.42.46:199][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.11-10.42.46:199][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.11-10.42.46:416][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.11-10.42.46:627][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.11-10.42.46:627][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.11-10.42.46:628][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.11-10.42.46:628][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.11-10.42.46:628][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.11-10.42.46:628][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.11-10.42.46:655][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.11-10.42.46:655][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.11-10.42.46:660][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.11-10.42.46:660][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.11-10.42.46:719][  0]LogEditorDataStorage: Initializing
[2025.06.11-10.42.46:719][  0]LogEditorDataStorage: Initialized
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:737][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:738][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.11-10.42.46:739][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.11-10.42.46:819][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.11-10.42.46:850][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.11-10.42.47:131][  0]LogUnrealEdMisc: Loading editor; pre map load, took 113.356
[2025.06.11-10.42.47:132][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.11-10.42.47:134][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.11-10.42.47:134][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.42.47:149][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.11-10.42.47:150][  0]InkPlusPlus: FStory::~FStory 000006792E29C610
[2025.06.11-10.42.47:151][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.54ms
[2025.06.11-10.42.47:228][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.11-10.42.47:228][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.11-10.42.47:228][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.11-10.42.47:228][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.11-10.42.47:228][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.11-10.42.47:273][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.11-10.42.47:274][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.11-10.42.47:548][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.11-10.42.47:913][  0]LogAssetRegistry: Display: Asset registry cache written as 106.4 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.11-10.42.48:117][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.11-10.42.49:983][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.11-10.42.52:414][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.52:417][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.11-10.42.52:417][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.11-10.42.52:417][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.52:417][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.11-10.42.52:417][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.11-10.42.53:020][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.11-10.42.54:926][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.54:927][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.11-10.42.54:927][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.11-10.42.54:927][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.11-10.42.54:927][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.11-10.42.54:927][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.11-10.42.55:035][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:036][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:037][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:038][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:039][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:040][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:041][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:043][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:043][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:045][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:046][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:047][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.55:048][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.11-10.42.56:700][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.11-10.42.56:733][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.11-10.42.56:747][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.11-10.42.56:747][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.11-10.42.57:139][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.42.57:143][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.11-10.42.57:153][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.11-10.42.57:897][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.11-10.42.58:263][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.42.58:267][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.11-10.42.58:434][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.42.58:437][  0]LogSkeletalMesh: Built Skeletal Mesh [1.69s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.11-10.42.58:673][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.11-10.42.58:690][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.11-10.42.58:692][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.11-10.42.58:692][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.11-10.42.59:055][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.42.59:056][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.11-10.43.00:541][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.11-10.43.00:977][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.00:981][  0]LogSkeletalMesh: Built Skeletal Mesh [0.44s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.11-10.43.01:045][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.11-10.43.01:045][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.11-10.43.01:045][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.11-10.43.01:045][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.11-10.43.01:045][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.11-10.43.01:760][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.11-10.43.01:762][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.11-10.43.01:762][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.11-10.43.01:762][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.11-10.43.01:762][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.11-10.43.01:762][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.11-10.43.01:762][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.11-10.43.01:906][  0]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps BeginCache
[2025.06.11-10.43.01:906][  0]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.11-10.43.01:917][  0]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far BeginCache
[2025.06.11-10.43.01:918][  0]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.11-10.43.01:921][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.11-10.43.01:922][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.11-10.43.01:972][  0]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.11-10.43.01:973][  0]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.11-10.43.01:991][  0]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.11-10.43.01:993][  0]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.11-10.43.02:019][  0]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.11-10.43.02:019][  0]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.11-10.43.02:122][  0]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal BeginCache
[2025.06.11-10.43.02:123][  0]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal BuildIndex From Cache
[2025.06.11-10.43.02:132][  0]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.11-10.43.02:132][  0]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.11-10.43.02:140][  0]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.11-10.43.02:140][  0]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.11-10.43.02:140][  0]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.11-10.43.02:140][  0]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.11-10.43.02:142][  0]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.11-10.43.02:142][  0]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.11-10.43.02:153][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.11-10.43.02:154][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.11-10.43.02:156][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.11-10.43.02:334][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.02:335][  0]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.11-10.43.02:463][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.11-10.43.02:497][  0]LogPoseSearch: af9e19fa6af4f79379007c0ce63cd543e2a68bcc - PSD_Dense_Crouch_Idle BeginCache
[2025.06.11-10.43.02:498][  0]LogPoseSearch: af9e19fa6af4f79379007c0ce63cd543e2a68bcc - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.11-10.43.02:521][  0]LogPoseSearch: e87fab46887878f88a1e636954252f6675d46fc6 - PSD_Dense_Crouch_Loop BeginCache
[2025.06.11-10.43.02:522][  0]LogPoseSearch: e87fab46887878f88a1e636954252f6675d46fc6 - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.11-10.43.02:562][  0]LogPoseSearch: ba16425d0776004701de0cb772f64cb39c494537 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.11-10.43.02:562][  0]LogPoseSearch: ba16425d0776004701de0cb772f64cb39c494537 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.11-10.43.02:601][  0]LogPoseSearch: cc211e9c34f15de1dc615cac8859cd356b1639ca - PSD_Dense_Crouch_Start BeginCache
[2025.06.11-10.43.02:602][  0]LogPoseSearch: cc211e9c34f15de1dc615cac8859cd356b1639ca - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.11-10.43.02:641][  0]LogPoseSearch: e4992f798d3c30031eb32ea237475e6604239ff2 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.11-10.43.02:642][  0]LogPoseSearch: e4992f798d3c30031eb32ea237475e6604239ff2 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.11-10.43.02:813][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.02:815][  0]LogSkeletalMesh: Built Skeletal Mesh [0.35s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.11-10.43.02:816][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.11-10.43.03:203][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.03:207][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.11-10.43.03:208][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.11-10.43.03:238][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.11-10.43.03:660][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.03:663][  0]LogSkeletalMesh: Built Skeletal Mesh [1.51s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.11-10.43.03:665][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.11-10.43.03:694][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.03:697][  0]LogSkeletalMesh: Built Skeletal Mesh [1.54s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.11-10.43.03:698][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.11-10.43.03:782][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.03:784][  0]LogSkeletalMesh: Built Skeletal Mesh [0.09s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.11-10.43.03:785][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.11-10.43.04:014][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.04:017][  0]LogSkeletalMesh: Built Skeletal Mesh [0.23s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.11-10.43.04:019][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.11-10.43.04:045][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.04:047][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.11-10.43.04:048][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.11-10.43.04:132][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.11-10.43.04:132][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.11-10.43.04:186][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.11-10.43.04:293][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.04:296][  0]LogSkeletalMesh: Built Skeletal Mesh [0.28s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.11-10.43.04:298][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.11-10.43.04:721][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.04:724][  0]LogSkeletalMesh: Built Skeletal Mesh [1.52s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.11-10.43.04:726][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.11-10.43.05:774][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.11-10.43.15:178][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.15:224][  0]LogSkeletalMesh: Built Skeletal Mesh [11.18s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.11-10.43.15:227][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.11-10.43.15:397][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.15:399][  0]LogSkeletalMesh: Built Skeletal Mesh [0.17s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.11-10.43.15:401][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.11-10.43.15:488][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.15:508][  0]LogSkeletalMesh: Built Skeletal Mesh [11.21s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.11-10.43.15:567][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.11-10.43.15:590][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.11-10.43.15:598][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 33/36 (Baoli_Child2_FaceMesh) ...
[2025.06.11-10.43.15:653][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.15:657][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.11-10.43.15:834][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.15:857][  0]LogSkeletalMesh: Built Skeletal Mesh [11.13s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.11-10.43.15:861][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 35/36 (Kellan_FaceMesh) ...
[2025.06.11-10.43.15:970][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.11-10.43.15:978][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.11-10.43.16:132][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.33ms
[2025.06.11-10.43.16:138][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.11-10.43.16:139][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.091ms to complete.
[2025.06.11-10.43.16:146][  0]LogUnrealEdMisc: Total Editor Startup Time, took 142.371
[2025.06.11-10.43.16:304][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.11-10.43.16:410][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.43.16:464][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.43.16:511][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.43.16:559][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.43.16:598][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:599][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.11-10.43.16:599][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:599][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.11-10.43.16:599][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:599][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.11-10.43.16:599][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:600][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.11-10.43.16:600][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:600][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.11-10.43.16:600][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:600][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.11-10.43.16:600][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:600][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.11-10.43.16:601][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:601][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.11-10.43.16:601][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:601][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.11-10.43.16:601][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.11-10.43.16:601][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.11-10.43.16:666][  0]LogSlate: Took 0.000173 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.11-10.43.16:700][  0]LogSlate: Took 0.002630 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/NotoColorEmoji.ttf' (7610K)
[2025.06.11-10.43.16:700][  0]LogSlate: Took 0.000132 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.11-10.43.16:701][  0]LogSlate: Took 0.000984 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansFallback.ttf' (3848K)
[2025.06.11-10.43.16:875][  0]LogStall: Startup...
[2025.06.11-10.43.16:879][  0]LogStall: Startup complete.
[2025.06.11-10.43.16:919][  0]LogLoad: (Engine Initialization) Total time: 143.14 seconds
[2025.06.11-10.43.17:033][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.17:142][  0]LogSourceControl: P4 execution time: 0.1093 seconds. Command: client -o super_dev
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.17:255][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.17:255][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.11-10.43.17:255][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.11-10.43.17:420][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.11-10.43.17:510][  0]LogSlate: Took 0.000207 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.11-10.43.17:512][  0]LogSlate: Took 0.000157 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.11-10.43.17:535][  0]LogSourceControl: P4 execution time: 0.1147 seconds. Command: fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini
[2025.06.11-10.43.17:581][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.11-10.43.17:581][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.11-10.43.17:583][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.11-10.43.17:584][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.11-10.43.17:584][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.11-10.43.17:650][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.11-10.43.17:650][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.11-10.43.17:651][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.11-10.43.17:651][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.11-10.43.17:651][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.11-10.43.17:709][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.11-10.43.17:709][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.11-10.43.17:764][  0]LogSlate: Took 0.000467 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.11-10.43.18:155][  0]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.11-10.43.18:219][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 13.56 ms. Compile time 9.06 ms, link time 4.45 ms.
[2025.06.11-10.43.18:226][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 58.35 ms. Compile time 49.56 ms, link time 8.66 ms.
[2025.06.11-10.43.18:295][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.43.18:305][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.11-10.43.18:305][  0]LogFab: Display: Logging in using exchange code
[2025.06.11-10.43.18:305][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.11-10.43.18:305][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.11-10.43.18:305][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.11-10.43.18:305][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 164 us
[2025.06.11-10.43.18:305][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.11-10.43.18:347][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.11-10.43.18:362][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 56.667 ms (total: 56.831 ms)
[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:362][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.11-10.43.18:362][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.11-10.43.18:363][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.11-10.43.18:498][  1]LogAssetRegistry: AssetRegistryGather time 1.0930s: AssetDataDiscovery 0.9319s, AssetDataGather 0.0369s, StoreResults 0.1243s. Wall time 54.7930s.
	NumCachedDirectories 0. NumUncachedDirectories 3071. NumCachedFiles 16170. NumUncachedFiles 4.
	BackgroundTickInterruptions 267.
[2025.06.11-10.43.18:567][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.11-10.43.18:572][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000030 seconds (updated 1 objects)
[2025.06.11-10.43.18:692][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.11-10.43.18:709][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.11-10.43.18:813][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.11-10.43.18:959][  2]LogSourceControl: Uncontrolled asset enumeration finished in 0.392923 seconds (Found 7983 uncontrolled assets)
[2025.06.11-10.43.19:493][ 19]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 37.509838
[2025.06.11-10.43.19:494][ 19]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.11-10.43.19:495][ 19]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 37.517826
[2025.06.11-10.43.19:777][ 53]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.11-10.43.19:841][ 61]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.11-10.43.19:852][ 61]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_0:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.43.19:887][ 61]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.11-10.43.19:889][ 61]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.11-10.43.19:889][ 61]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.11-10.43.19:891][ 61]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.11-10.43.19:892][ 61]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.11-10.43.19:893][ 61]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.11-10.43.19:893][ 61]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.11-10.43.19:894][ 61]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.11-10.43.19:894][ 61]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.11-10.43.19:895][ 61]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.11-10.43.19:896][ 61]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.11-10.43.19:898][ 61]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.11-10.43.19:898][ 61]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.11-10.43.19:899][ 61]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.11-10.43.19:899][ 61]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.11-10.43.19:900][ 61]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.11-10.43.19:900][ 61]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.11-10.43.19:901][ 61]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.11-10.43.19:901][ 61]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.11-10.43.19:902][ 61]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.11-10.43.19:902][ 61]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.11-10.43.19:902][ 61]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.11-10.43.19:903][ 61]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.11-10.43.19:904][ 61]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.11-10.43.19:905][ 61]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.11-10.43.19:906][ 61]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.11-10.43.19:906][ 61]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.11-10.43.19:907][ 61]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.11-10.43.19:907][ 61]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.11-10.43.19:908][ 61]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.11-10.43.19:909][ 61]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.11-10.43.19:909][ 61]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.11-10.43.19:910][ 61]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.11-10.43.19:911][ 61]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.11-10.43.19:911][ 61]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.11-10.43.19:911][ 61]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.11-10.43.19:912][ 61]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.11-10.43.19:913][ 61]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.11-10.43.19:913][ 61]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.11-10.43.19:913][ 61]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.11-10.43.19:914][ 61]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles BeginCache
[2025.06.11-10.43.19:915][ 61]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.11-10.43.19:915][ 61]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.11-10.43.19:916][ 61]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.11-10.43.19:946][ 62]LogD3D12RHI: Creating RTPSO with 21 shaders (13 cached, 8 new) took 11.05 ms. Compile time 8.28 ms, link time 2.72 ms.
[2025.06.11-10.43.19:954][ 62]LogD3D12RHI: Creating RTPSO with 227 shaders (197 cached, 30 new) took 24.68 ms. Compile time 13.22 ms, link time 11.28 ms.
[2025.06.11-10.43.19:962][ 62]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.11-10.43.19:976][ 62]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.19:976][ 62]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.19:977][ 62]LogPoseSearch: UPoseSearchLibrary::UpdateMotionMatchingState invalid search result : ForceInterrupt [true], CanAdvance [false], Indexing [true], Databases [PSD_Dense_Stand_Idles] 
[2025.06.11-10.43.20:004][ 63]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.11-10.43.20:087][ 66]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.11-10.43.20:097][ 66]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_3:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.20:097][ 66]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_3:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.20:598][ 74]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 38.566620
[2025.06.11-10.43.20:602][ 74]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.06.11-10.43.20:602][ 74]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 38.566620, Update Interval: 303.061615
[2025.06.11-10.43.21:145][121]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.11-10.43.21:232][122]LogD3D12RHI: Creating RTPSO with 229 shaders (0 cached, 1 new) took 11.38 ms. Compile time 1.30 ms, link time 9.99 ms.
[2025.06.11-10.43.21:337][125]LogD3D12RHI: Creating RTPSO with 230 shaders (0 cached, 1 new) took 12.22 ms. Compile time 1.63 ms, link time 10.52 ms.
[2025.06.11-10.43.21:343][125]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.11-10.43.21:378][126]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.11-10.43.21:389][126]LogActor: Warning: BP_Bed_C /Engine/Transient.World_6:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.43.21:427][129]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.21:427][129]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.33:245][467]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.33:245][467]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.33:725][479]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset H:/P4/dev/Baoli/Content/Blueprints/CBP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/GM_Sandbox.uasset H:/P4/dev/Baoli/Content/Blueprints/Kettle_BP.uasset H:/P4/dev/Baoli/Content/Blueprints/QTE.uasset'
[2025.06.11-10.43.33:885][485]LogSourceControl: P4 execution time: 0.1597 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset H:/P4/dev/Baoli/Content/Blueprints/CBP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/GM_Sandbox.uasset H:/P4/dev/Baoli/Content/Blueprints/Kettle_BP.uasset H:/P4/dev/Baoli/Content/Blueprints/QTE.uasset
[2025.06.11-10.43.37:834][647]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.11-10.43.37:834][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.11-10.43.37:854][647]LogTemp: Display: rdBPTools: Failed to load rdBPTools config ini file
[2025.06.11-10.43.37:854][647]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.11-10.43.37:865][647]LogStreaming: Display: FlushAsyncLoading(356): 1 QueuedPackages, 0 AsyncPackages
[2025.06.11-10.43.38:010][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.38:115][647]LogSourceControl: P4 execution time: 0.1051 seconds. Command: client -o super_dev
[2025.06.11-10.43.40:990][647]LogBlueprintEditor: Perf: 3.1 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.11-10.43.40:990][647]LogBlueprintEditor: Perf: 3.0 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.11-10.43.41:917][647]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.11-10.43.41:917][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.11-10.43.41:937][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.11-10.43.41:966][647]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.11-10.43.41:967][647]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.41:967][647]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.42:032][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.42:137][647]LogSourceControl: P4 execution time: 0.1061 seconds. Command: client -o super_dev
[2025.06.11-10.43.42:156][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.42:260][647]LogSourceControl: P4 execution time: 0.1049 seconds. Command: client -o super_dev
[2025.06.11-10.43.42:308][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.11-10.43.42:438][647]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_AO.CR_AO
[2025.06.11-10.43.42:439][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.11-10.43.42:481][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.11-10.43.42:483][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.42:494][647]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_12:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.11-10.43.42:494][647]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_12:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.11-10.43.42:588][647]LogSourceControl: P4 execution time: 0.1051 seconds. Command: client -o super_dev
[2025.06.11-10.43.42:611][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.42:715][647]LogSourceControl: P4 execution time: 0.1052 seconds. Command: client -o super_dev
[2025.06.11-10.43.42:787][647]LogUObjectHash: Compacting FUObjectHashTables data took   2.70ms
[2025.06.11-10.43.43:081][647]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager
[2025.06.11-10.43.43:081][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.11-10.43.43:128][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.11-10.43.43:155][647]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_14:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.11-10.43.43:155][647]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_14:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.11-10.43.43:205][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.43:260][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.43:325][647]LogSourceControl: P4 execution time: 0.1193 seconds. Command: client -o super_dev
[2025.06.11-10.43.43:371][647]LogUObjectHash: Compacting FUObjectHashTables data took   1.82ms
[2025.06.11-10.43.43:380][647]LogSourceControl: P4 execution time: 0.1199 seconds. Command: client -o super_dev
[2025.06.11-10.43.43:783][647]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.43.43:888][647]LogSourceControl: P4 execution time: 0.1050 seconds. Command: client -o super_dev
[2025.06.11-10.43.43:909][647]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.06.11-10.43.43:965][647]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.43:966][647]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:003][647]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:032][647]LogSlate: Took 0.000256 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.11-10.43.44:033][647]LogSlate: Took 0.000109 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.11-10.43.44:135][648]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:143][648]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:172][649]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:176][649]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:177][649]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:193][650]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:194][650]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:196][650]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:201][650]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:210][651]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.06.11-10.43.44:222][651]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:226][651]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:241][652]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:245][652]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:259][653]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:263][653]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:264][653]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:275][654]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:275][654]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:277][654]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.43.44:282][654]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.43.44:287][655]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.41:840][310]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.11-10.45.41:850][310]LogPlayLevel: [PlayLevel] Compiling ABP_SandboxCharacter before play...
[2025.06.11-10.45.41:852][310]LogPlayLevel: [PlayLevel]   Compiling CR_AO as a dependent...
[2025.06.11-10.45.42:014][310]LogPlayLevel: [PlayLevel] Compiling ABP_SandboxCharacter before play...
[2025.06.11-10.45.42:015][310]LogPlayLevel: [PlayLevel]   Compiling CR_HeadManager as a dependent...
[2025.06.11-10.45.42:253][310]LogUObjectHash: Compacting FUObjectHashTables data took   2.28ms
[2025.06.11-10.45.42:256][310]LogPlayLevel: PlayLevel: Blueprint regeneration took 408 ms (4 blueprints)
[2025.06.11-10.45.42:261][310]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.11-10.45.42:261][310]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.11-10.45.42:262][310]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.11-10.45.42:322][310]LogPlayLevel: PIE: StaticDuplicateObject took: (0.060273s)
[2025.06.11-10.45.42:322][310]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060357s)
[2025.06.11-10.45.42:356][310]LogUObjectHash: Compacting FUObjectHashTables data took   1.78ms
[2025.06.11-10.45.42:359][310]r.RayTracing.Culling = "0"
[2025.06.11-10.45.42:359][310]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.11-10.45.42:359][310]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.11-10.45.42:361][310]LogPlayLevel: PIE: World Init took: (0.001791s)
[2025.06.11-10.45.42:362][310]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.11-10.45.42:362][310]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.11-10.45.42:362][310]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.11-10.45.42:362][310]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.11-10.45.42:362][310]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.11-10.45.42:362][310]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.11-10.45.42:365][310]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.11-10.45.42:402][310]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.11-10.45.42:402][310]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.11-10.45.42:402][310]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.11-10.45.42:402][310]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.11-10.45.42:402][310]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.11-10.45.42:402][310]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.11-10.45.42:405][310]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.11-10.45.42:405][310]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.11-10.45.42:405][310]LogInit: FAudioDevice initialized with ID 2.
[2025.06.11-10.45.42:405][310]LogAudio: Display: Audio Device (ID: 2) registered with world 'DefaultLevel'.
[2025.06.11-10.45.42:405][310]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.11-10.45.42:405][310]LogWindows: WindowsPlatformFeatures enabled
[2025.06.11-10.45.42:405][310]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.11-10.45.42:408][310]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.11-10.45.42:435][310]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.11-10.45.42:443][310]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.11-16.15.42
[2025.06.11-10.45.42:450][310]LogWorld: Bringing up level for play took: 0.041834
[2025.06.11-10.45.42:453][310]LogOnline: OSS: Created online subsystem instance for: :Context_18
[2025.06.11-10.45.42:455][310]LogAnimation: Warning: SetBlendSpace called on an invalid context or with an invalid type
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.11-10.45.42:471][310]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.11-10.45.42:484][310]PIE: Server logged in
[2025.06.11-10.45.42:484][310]PIE: Play in editor total start time 0.637 seconds.
[2025.06.11-10.45.42:488][310]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.42:488][310]LogTemp: Warning: Speed: 8.17, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.42:685][311]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.42:685][311]LogTemp: Warning: Speed: 400.17, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.42:749][311]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.11-10.45.42:799][311]LogD3D12RHI: Creating RTPSO with 24 shaders (20 cached, 4 new) took 15.08 ms. Compile time 9.01 ms, link time 5.99 ms.
[2025.06.11-10.45.42:800][311]LogD3D12RHI: Creating RTPSO with 230 shaders (0 cached, 1 new) took 16.28 ms. Compile time 5.65 ms, link time 10.48 ms.
[2025.06.11-10.45.42:897][312]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.42:897][312]LogTemp: Warning: Speed: 616.44, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:036][313]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:036][313]LogTemp: Warning: Speed: 753.69, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:107][314]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:107][314]LogTemp: Warning: Speed: 822.06, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:159][315]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:159][315]LogTemp: Warning: Speed: 873.36, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:237][316]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:237][316]LogTemp: Warning: Speed: 914.48, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:334][317]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.43:335][317]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.43:339][317]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:339][317]LogTemp: Warning: Speed: 979.72, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:378][318]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'UE.Wave Player.Stereo (v1.0)': Interface change detected.
[2025.06.11-10.45.43:378][318]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'Array.Random Get.WaveAsset:Array': Newer version 'v1.1' found.
[2025.06.11-10.45.43:382][318]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:382][318]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:418][319]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:418][319]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:462][320]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:462][320]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:497][321]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:497][321]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:537][322]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:537][322]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:591][323]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:592][323]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:632][324]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:632][324]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:680][325]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.43:682][325]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.43:687][325]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:687][325]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:718][326]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:718][326]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:743][327]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:743][327]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:763][328]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:763][328]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:792][329]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:792][329]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:839][330]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:839][330]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:856][331]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:856][331]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:881][332]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:881][332]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:934][333]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:934][333]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:955][334]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:955][334]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.43:986][335]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.43:986][335]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:007][336]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:008][336]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:041][337]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:041][337]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:068][338]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:069][338]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:098][339]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:098][339]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:127][340]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:127][340]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:157][341]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:157][341]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:202][342]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:202][342]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:234][343]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:234][343]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:263][344]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:263][344]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:313][345]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:313][345]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:339][346]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:340][346]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:373][347]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:373][347]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:422][348]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:422][348]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:459][349]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:459][349]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:489][350]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:489][350]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:526][351]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:526][351]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:556][352]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:556][352]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:587][353]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:587][353]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:618][354]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:618][354]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:649][355]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:649][355]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:679][356]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:679][356]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:708][357]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:708][357]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:742][358]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:742][358]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:771][359]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:771][359]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:801][360]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:802][360]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:831][361]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:831][361]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:868][362]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:868][362]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:898][363]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:899][363]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:929][364]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:929][364]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.44:972][365]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.44:972][365]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:013][366]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:013][366]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:049][367]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:049][367]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:080][368]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:080][368]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:111][369]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:111][369]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:141][370]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:141][370]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:171][371]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:171][371]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:202][372]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:202][372]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:231][373]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:231][373]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:261][374]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:261][374]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:293][375]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:293][375]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:325][376]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:325][376]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:355][377]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:355][377]LogTemp: Warning: Speed: 60.31, OnGround: true, Moving: true, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:355][377]LogTemp: Warning: STARTING MOVEMENT WINDOW
[2025.06.11-10.45.45:355][377]LogTemp: Warning: INCREMENTING frame count to: 1
[2025.06.11-10.45.45:386][378]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] true
[2025.06.11-10.45.45:386][378]LogTemp: Warning: Speed: 123.97, OnGround: true, Moving: true, WasMoving: true, InWindow: true, FrameCount: 1
[2025.06.11-10.45.45:386][378]LogTemp: Warning: INCREMENTING frame count to: 2
[2025.06.11-10.45.45:386][378]LogTemp: Warning: STOPPING - frame limit reached: 2 >= 2
[2025.06.11-10.45.45:419][379]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:419][379]LogTemp: Warning: Speed: 192.86, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:451][380]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:451][380]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:479][381]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:479][381]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:510][382]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:510][382]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:541][383]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:541][383]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:574][384]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:574][384]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:605][385]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:605][385]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:676][386]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:676][386]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:759][387]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.45:759][387]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.45:762][387]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:762][387]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:808][388]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:809][388]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:842][389]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:842][389]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:881][390]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:881][390]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:918][391]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:918][391]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:953][392]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:953][392]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.45:993][393]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.45:993][393]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:029][394]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:030][394]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:064][395]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.46:064][395]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.46:068][395]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:068][395]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:084][396]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:084][396]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:111][397]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:111][397]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:143][398]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:143][398]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:172][399]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:172][399]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:211][400]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:211][400]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:240][401]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:240][401]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:272][402]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:272][402]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:302][403]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:302][403]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:334][404]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:334][404]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:363][405]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:363][405]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:394][406]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:394][406]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:433][407]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:433][407]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:464][408]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:464][408]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:494][409]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:494][409]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:524][410]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:524][410]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:558][411]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:558][411]LogTemp: Warning: Speed: 199.13, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:589][412]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:589][412]LogTemp: Warning: Speed: 44.58, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:617][413]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:617][413]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:652][414]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:652][414]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:681][415]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:681][415]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:710][416]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:710][416]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:738][417]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:738][417]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:768][418]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:768][418]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:797][419]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:798][419]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:826][420]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:826][420]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:859][421]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:859][421]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:888][422]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:888][422]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:918][423]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:918][423]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:950][424]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:950][424]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.46:982][425]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.46:982][425]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:012][426]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.47:012][426]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:042][427]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.47:042][427]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:076][428]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.47:076][428]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:107][429]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.47:108][429]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:137][430]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.47:138][430]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:168][431]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.45.47:168][431]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.45.47:201][431]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.45.47:201][431]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.11-10.45.47:204][431]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.11-10.45.47:204][431]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.11-10.45.47:205][431]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.11-10.45.47:208][431]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.45.47:225][431]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.11-10.45.47:236][431]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.45.47:296][431]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.45.47:298][431]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.11-10.45.47:299][431]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.11-10.45.47:301][431]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.11-10.45.47:313][431]LogUObjectHash: Compacting FUObjectHashTables data took   2.31ms
[2025.06.11-10.45.47:363][432]LogPlayLevel: Display: Destroying online subsystem :Context_18
[2025.06.11-10.45.47:437][433]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_9
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.45.47:438][433]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_9
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.46.41:572][299]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.11-10.46.41:588][299]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.11-10.46.41:588][299]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.11-10.46.41:649][299]LogPlayLevel: PIE: StaticDuplicateObject took: (0.061834s)
[2025.06.11-10.46.41:650][299]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.061915s)
[2025.06.11-10.46.41:685][299]LogUObjectHash: Compacting FUObjectHashTables data took   1.82ms
[2025.06.11-10.46.41:689][299]r.RayTracing.Culling = "0"
[2025.06.11-10.46.41:689][299]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.11-10.46.41:689][299]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.11-10.46.41:691][299]LogPlayLevel: PIE: World Init took: (0.001877s)
[2025.06.11-10.46.41:692][299]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.11-10.46.41:692][299]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.11-10.46.41:692][299]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.11-10.46.41:692][299]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.11-10.46.41:692][299]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.11-10.46.41:692][299]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.11-10.46.41:695][299]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.11-10.46.41:732][299]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.11-10.46.41:732][299]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.11-10.46.41:732][299]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.11-10.46.41:732][299]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.11-10.46.41:734][299]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.11-10.46.41:734][299]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.11-10.46.41:735][299]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.11-10.46.41:735][299]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.11-10.46.41:736][299]LogInit: FAudioDevice initialized with ID 3.
[2025.06.11-10.46.41:736][299]LogAudio: Display: Audio Device (ID: 3) registered with world 'DefaultLevel'.
[2025.06.11-10.46.41:736][299]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.11-10.46.41:736][299]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.11-10.46.41:739][299]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.11-10.46.41:767][299]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.11-10.46.41:774][299]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.11-16.16.41
[2025.06.11-10.46.41:782][299]LogWorld: Bringing up level for play took: 0.042284
[2025.06.11-10.46.41:785][299]LogOnline: OSS: Created online subsystem instance for: :Context_19
[2025.06.11-10.46.41:786][299]LogAnimation: Warning: SetBlendSpace called on an invalid context or with an invalid type
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.11-10.46.41:802][299]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.11-10.46.41:803][299]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.11-10.46.41:815][299]PIE: Server logged in
[2025.06.11-10.46.41:817][299]PIE: Play in editor total start time 0.235 seconds.
[2025.06.11-10.46.41:818][299]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.41:818][299]LogTemp: Warning: Speed: 28.89, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.41:881][300]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.41:881][300]LogTemp: Warning: Speed: 325.93, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.41:963][300]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.11-10.46.42:066][301]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:066][301]LogTemp: Warning: Speed: 509.40, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:156][302]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:156][302]LogTemp: Warning: Speed: 597.65, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:199][303]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:199][303]LogTemp: Warning: Speed: 639.33, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:216][304]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:216][304]LogTemp: Warning: Speed: 654.88, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:239][305]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:239][305]LogTemp: Warning: Speed: 678.92, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:280][306]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:280][306]LogTemp: Warning: Speed: 719.80, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:295][307]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:295][307]LogTemp: Warning: Speed: 734.64, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:324][308]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:324][308]LogTemp: Warning: Speed: 762.79, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:356][309]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:356][309]LogTemp: Warning: Speed: 793.85, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:396][310]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:396][310]LogTemp: Warning: Speed: 832.77, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:415][311]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:415][311]LogTemp: Warning: Speed: 852.24, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:442][312]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:442][312]LogTemp: Warning: Speed: 878.58, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:495][313]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:495][313]LogTemp: Warning: Speed: 929.66, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:510][314]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:510][314]LogTemp: Warning: Speed: 944.24, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:545][315]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:545][315]LogTemp: Warning: Speed: 979.40, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:566][316]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:566][316]LogTemp: Warning: Speed: 1000.07, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:595][317]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:596][317]LogTemp: Warning: Speed: 1028.64, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:621][318]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:622][318]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:664][319]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:664][319]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:680][320]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:680][320]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:706][321]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:706][321]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:737][322]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:737][322]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:776][323]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:776][323]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:796][324]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:797][324]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:827][325]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:827][325]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:871][326]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:871][326]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:885][327]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:885][327]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:908][328]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:909][328]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:948][329]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:949][329]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:984][330]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:984][330]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.42:998][331]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.42:998][331]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:027][332]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:027][332]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:069][333]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:069][333]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:084][334]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:084][334]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:110][335]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:110][335]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:139][336]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:139][336]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:176][337]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:176][337]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:200][338]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:200][338]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:227][339]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:229][339]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:256][340]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:257][340]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:286][341]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:286][341]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:314][342]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:314][342]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:342][343]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:342][343]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:370][344]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:370][344]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:398][345]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:398][345]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:426][346]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:427][346]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:455][347]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:455][347]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:484][348]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:484][348]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:513][349]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:513][349]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:539][350]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:539][350]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:565][351]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:565][351]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:592][352]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:592][352]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:620][353]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:620][353]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:648][354]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:648][354]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:683][355]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:683][355]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:709][356]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:709][356]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:735][357]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:735][357]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:765][358]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:765][358]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:793][359]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:794][359]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:819][360]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:819][360]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:847][361]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:848][361]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:874][362]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:874][362]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:904][363]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:904][363]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:933][364]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:933][364]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:960][365]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:960][365]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.43:987][366]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.43:987][366]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:013][367]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:013][367]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:041][368]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:041][368]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:085][369]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:085][369]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:139][370]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:140][370]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:189][371]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:189][371]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:216][372]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:216][372]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:244][373]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:244][373]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:282][374]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:282][374]LogTemp: Warning: Speed: 69.32, OnGround: true, Moving: true, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:282][374]LogTemp: Warning: STARTING MOVEMENT WINDOW
[2025.06.11-10.46.44:282][374]LogTemp: Warning: INCREMENTING frame count to: 1
[2025.06.11-10.46.44:313][375]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] true
[2025.06.11-10.46.44:313][375]LogTemp: Warning: Speed: 138.06, OnGround: true, Moving: true, WasMoving: true, InWindow: true, FrameCount: 1
[2025.06.11-10.46.44:314][375]LogTemp: Warning: INCREMENTING frame count to: 2
[2025.06.11-10.46.44:314][375]LogTemp: Warning: STOPPING - frame limit reached: 2 >= 2
[2025.06.11-10.46.44:350][376]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:350][376]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:382][377]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:382][377]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:423][378]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:423][378]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:450][379]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:450][379]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:480][380]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:480][380]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:507][381]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:507][381]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:535][382]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:535][382]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:566][383]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:566][383]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:593][384]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:593][384]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:619][385]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:619][385]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:645][386]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:645][386]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:673][387]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:674][387]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:702][388]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:702][388]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:733][389]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:733][389]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:758][390]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:759][390]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:787][391]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:787][391]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:816][392]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:816][392]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:844][393]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:844][393]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:871][394]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:871][394]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:899][395]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:899][395]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:925][396]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:925][396]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:957][397]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:957][397]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.44:987][398]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.44:987][398]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:014][399]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:014][399]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:041][400]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:041][400]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:067][401]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:067][401]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:093][402]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:093][402]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:124][403]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:125][403]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:154][404]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:154][404]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:181][405]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:181][405]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:213][406]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:214][406]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:239][407]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:239][407]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:266][408]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:266][408]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:293][409]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:293][409]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:327][410]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:327][410]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:349][411]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:349][411]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:377][412]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:377][412]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:407][413]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:407][413]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:437][414]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:437][414]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:463][415]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:463][415]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:492][416]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:492][416]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:529][417]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:529][417]LogTemp: Warning: Speed: 34.14, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:567][418]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:567][418]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:604][419]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:605][419]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:639][420]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:640][420]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:669][421]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:669][421]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:694][422]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:695][422]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:723][423]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:723][423]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:752][424]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.46.45:752][424]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.46.45:791][424]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.46.45:791][424]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.11-10.46.45:794][424]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.11-10.46.45:794][424]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.11-10.46.45:795][424]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.11-10.46.45:798][424]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.46.45:805][424]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.11-10.46.45:818][424]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.46.45:876][424]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.46.45:879][424]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.11-10.46.45:879][424]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.11-10.46.45:881][424]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.11-10.46.45:893][424]LogUObjectHash: Compacting FUObjectHashTables data took   2.06ms
[2025.06.11-10.46.45:937][425]LogPlayLevel: Display: Destroying online subsystem :Context_19
[2025.06.11-10.47.10:656][385]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.10:657][385]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.12:961][453]LogConfig: Branch 'rdBPToolsConfig' had been unloaded. Reloading on-demand took 0.62ms
[2025.06.11-10.47.17:286][592]LogUObjectGlobals: Reloading 1 Package(s):
	Asset Name: /Game/Blueprints/BP_BaoliCharacter
[2025.06.11-10.47.17:323][592]LogUObjectHash: Compacting FUObjectHashTables data took   2.02ms
[2025.06.11-10.47.17:362][592]LogStreaming: Display: FlushAsyncLoading(564): 1 QueuedPackages, 0 AsyncPackages
[2025.06.11-10.47.18:268][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.DirectionalLightComponent_21) Not registered. Aborting.
[2025.06.11-10.47.18:268][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.SkyLightComponent_7) Not registered. Aborting.
[2025.06.11-10.47.18:268][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.LineBatchComponent_37) Not registered. Aborting.
[2025.06.11-10.47.18:268][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.EditorFloorComp) Not registered. Aborting.
[2025.06.11-10.47.18:268][592]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.11-10.47.18:268][592]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.47.18:453][592]LogUObjectHash: Compacting FUObjectHashTables data took   2.47ms
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.DirectionalLightComponent_6) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.SkyLightComponent_2) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.LineBatchComponent_17) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.StaticMeshComponent_5) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.DirectionalLightComponent_7) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.DirectionalLightComponent_8) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogActorComponent: UnregisterComponent: (/Engine/Transient.StaticMeshComponent_6) Not registered. Aborting.
[2025.06.11-10.47.18:477][592]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.11-10.47.18:477][592]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.47.18:507][592]LogSpawn: Warning: UWorld::DestroyActor: World has no context! World: World_2, Actor: /Engine/Transient.World_2:PersistentLevel.BP_BaoliCharacter_C_1
[2025.06.11-10.47.18:534][592]LogProperty: Warning: Serialized BlueprintGeneratedClass /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter_C for a property of BlueprintGeneratedClass /Game/Blueprints/BP_BaoliCharacter_DEADPACKAGE_1.BP_BaoliCharacter_C. Reference will be nulled.
    ReferencingObject = Package /Engine/Transient
    Property = ObjectProperty /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:SandboxCharacter
    Item = BP_BaoliCharacter_C /Engine/Transient.World_2:PersistentLevel.BP_BaoliCharacter_C_0
[2025.06.11-10.47.18:571][592]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: af9e19fa6af4f79379007c0ce63cd543e2a68bcc - PSD_Dense_Crouch_Idle Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: e87fab46887878f88a1e636954252f6675d46fc6 - PSD_Dense_Crouch_Loop Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: ba16425d0776004701de0cb772f64cb39c494537 - PSD_Dense_Crouch_Pivot Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: cc211e9c34f15de1dc615cac8859cd356b1639ca - PSD_Dense_Crouch_Start Cancelled because of OnPackageReloaded
[2025.06.11-10.47.18:571][592]LogPoseSearch: e4992f798d3c30031eb32ea237475e6604239ff2 - PSD_Dense_Crouch_Stops Cancelled because of OnPackageReloaded
[2025.06.11-10.47.19:024][592]LogActor: Warning: BP_Bed_C /Game/Levels/DefaultLevel.DefaultLevel:PersistentLevel.TestBed_C_1 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.47.19:025][592]LogActor: Warning: BP_Bed_C /Engine/Transient.World_6:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.47.19:422][592]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.11-10.47.19:508][592]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_3:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.19:828][592]LogActor: Warning: BP_Almirah_C /Game/Levels/DefaultLevel.DefaultLevel:PersistentLevel.BP_Almirah_C_1 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.47.19:830][592]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_0:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.47.20:232][592]LogUObjectHash: Compacting FUObjectHashTables data took   3.38ms
[2025.06.11-10.47.20:235][592]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.11-10.47.20:236][592]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.06.11-10.47.20:262][592]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.11-10.47.20:418][592]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.47.20:468][592]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps BeginCache
[2025.06.11-10.47.20:468][592]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.11-10.47.20:469][592]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.11-10.47.20:469][592]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.11-10.47.20:470][592]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.11-10.47.20:470][592]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.11-10.47.20:471][592]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.11-10.47.20:472][592]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.11-10.47.20:472][592]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.11-10.47.20:473][592]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.11-10.47.20:474][592]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.11-10.47.20:474][592]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.11-10.47.20:475][592]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.11-10.47.20:475][592]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.11-10.47.20:475][592]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.11-10.47.20:476][592]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.11-10.47.20:476][592]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.11-10.47.20:477][592]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.11-10.47.20:477][592]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.11-10.47.20:478][592]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.11-10.47.20:478][592]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.11-10.47.20:479][592]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.11-10.47.20:479][592]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.11-10.47.20:480][592]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.11-10.47.20:480][592]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.11-10.47.20:481][592]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.11-10.47.20:481][592]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.11-10.47.20:482][592]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.11-10.47.20:482][592]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles BeginCache
[2025.06.11-10.47.20:482][592]LogPoseSearch: UPoseSearchLibrary::UpdateMotionMatchingState invalid search result : ForceInterrupt [true], CanAdvance [false], Indexing [true], Databases [PSD_Dense_Stand_Idles] 
[2025.06.11-10.47.20:482][592]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.11-10.47.20:503][592]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.11-10.47.20:523][592]LogSourceControl: P4 execution time: 0.1046 seconds. Command: client -o super_dev
[2025.06.11-10.47.20:610][592]LogSourceControl: P4 execution time: 0.1083 seconds. Command: client -o super_dev
[2025.06.11-10.47.20:736][592]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.06.11-10.47.20:748][592]LogPoseSearch: UPoseSearchLibrary::UpdateMotionMatchingState invalid search result : ForceInterrupt [false], CanAdvance [false], Indexing [true], Databases [PSD_Dense_Stand_Idles] 
[2025.06.11-10.47.20:787][592]LogPoseSearch: UPoseSearchLibrary::UpdateMotionMatchingState invalid search result : ForceInterrupt [false], CanAdvance [false], Indexing [true], Databases [PSD_Dense_Stand_Idles] 
[2025.06.11-10.47.20:910][592]LogUObjectHash: Compacting FUObjectHashTables data took   1.94ms
[2025.06.11-10.47.20:917][592]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.47.20:917][592]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.47.20:917][592]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.47.20:917][592]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.47.20:918][592]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.47.21:126][592]LogStreaming: Display: FlushAsyncLoading(567): 1 QueuedPackages, 0 AsyncPackages
[2025.06.11-10.47.21:275][593]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/11 16:12:35
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.11-10.47.21:287][593]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.11-10.47.21:371][595]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.06.11-10.47.21:400][596]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_0:PersistentLevel.BP_Almirah_C_1 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.47.21:466][597]LogActor: Warning: BP_Bed_C /Engine/Transient.World_6:PersistentLevel.BP_Bed_C_1 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.11-10.47.21:504][598]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.21:505][598]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.21:509][598]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.21:510][598]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0413
[2025.06.11-10.47.28:389][939]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.11-10.47.28:407][939]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.11-10.47.28:407][939]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.11-10.47.28:475][939]LogPlayLevel: PIE: StaticDuplicateObject took: (0.068173s)
[2025.06.11-10.47.28:475][939]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.068222s)
[2025.06.11-10.47.28:511][939]LogUObjectHash: Compacting FUObjectHashTables data took   1.89ms
[2025.06.11-10.47.28:514][939]r.RayTracing.Culling = "0"
[2025.06.11-10.47.28:514][939]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.11-10.47.28:514][939]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.11-10.47.28:517][939]LogPlayLevel: PIE: World Init took: (0.001780s)
[2025.06.11-10.47.28:517][939]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.11-10.47.28:517][939]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.11-10.47.28:517][939]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.11-10.47.28:517][939]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.11-10.47.28:517][939]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.11-10.47.28:517][939]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.11-10.47.28:520][939]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.11-10.47.28:555][939]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.11-10.47.28:555][939]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.11-10.47.28:555][939]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.11-10.47.28:556][939]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.11-10.47.28:558][939]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.11-10.47.28:558][939]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.11-10.47.28:559][939]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.11-10.47.28:559][939]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.11-10.47.28:559][939]LogInit: FAudioDevice initialized with ID 4.
[2025.06.11-10.47.28:559][939]LogAudio: Display: Audio Device (ID: 4) registered with world 'DefaultLevel'.
[2025.06.11-10.47.28:559][939]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.11-10.47.28:559][939]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.11-10.47.28:563][939]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.11-10.47.28:589][939]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.11-10.47.28:598][939]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.11-16.17.28
[2025.06.11-10.47.28:604][939]LogWorld: Bringing up level for play took: 0.041485
[2025.06.11-10.47.28:607][939]LogOnline: OSS: Created online subsystem instance for: :Context_24
[2025.06.11-10.47.28:608][939]LogAnimation: Warning: SetBlendSpace called on an invalid context or with an invalid type
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.11-10.47.28:625][939]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.11-10.47.28:627][939]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far BeginCache
[2025.06.11-10.47.28:627][939]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.11-10.47.28:628][939]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.11-10.47.28:629][939]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.11-10.47.28:629][939]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.11-10.47.28:630][939]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.11-10.47.28:630][939]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.11-10.47.28:630][939]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.11-10.47.28:631][939]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.11-10.47.28:631][939]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.11-10.47.28:631][939]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.11-10.47.28:632][939]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.11-10.47.28:633][939]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.11-10.47.28:633][939]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.11-10.47.28:634][939]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.11-10.47.28:634][939]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.11-10.47.28:634][939]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.11-10.47.28:635][939]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.11-10.47.28:635][939]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.11-10.47.28:636][939]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.11-10.47.28:637][939]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.11-10.47.28:637][939]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.11-10.47.28:637][939]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.11-10.47.28:638][939]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.11-10.47.28:638][939]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.11-10.47.28:639][939]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.11-10.47.28:639][939]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.11-10.47.28:639][939]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.11-10.47.28:640][939]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.11-10.47.28:641][939]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.11-10.47.28:642][939]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.11-10.47.28:642][939]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal BeginCache
[2025.06.11-10.47.28:642][939]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.11-10.47.28:642][939]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal BuildIndex From Cache
[2025.06.11-10.47.28:651][939]PIE: Server logged in
[2025.06.11-10.47.28:652][939]PIE: Play in editor total start time 0.253 seconds.
[2025.06.11-10.47.28:653][939]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.28:653][939]LogTemp: Warning: Speed: 28.64, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.28:717][940]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.28:717][940]LogTemp: Warning: Speed: 345.58, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.28:796][940]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.11-10.47.28:900][941]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.28:900][941]LogTemp: Warning: Speed: 525.83, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.28:984][942]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.28:984][942]LogTemp: Warning: Speed: 608.15, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:023][943]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:023][943]LogTemp: Warning: Speed: 646.15, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:039][944]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:039][944]LogTemp: Warning: Speed: 661.73, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:062][945]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:062][945]LogTemp: Warning: Speed: 685.28, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:106][946]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:106][946]LogTemp: Warning: Speed: 727.09, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:122][947]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:122][947]LogTemp: Warning: Speed: 743.57, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:153][948]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:153][948]LogTemp: Warning: Speed: 774.10, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:186][949]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:186][949]LogTemp: Warning: Speed: 806.13, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:227][950]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:227][950]LogTemp: Warning: Speed: 847.21, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:244][951]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:244][951]LogTemp: Warning: Speed: 863.65, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:274][952]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:274][952]LogTemp: Warning: Speed: 891.82, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:321][953]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:321][953]LogTemp: Warning: Speed: 936.11, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:339][954]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:339][954]LogTemp: Warning: Speed: 955.83, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:371][955]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:371][955]LogTemp: Warning: Speed: 986.32, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:401][956]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:401][956]LogTemp: Warning: Speed: 1015.66, OnGround: false, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:431][957]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:431][957]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:473][958]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:473][958]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:489][959]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:489][959]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:521][960]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:521][960]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:553][961]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:553][961]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:589][962]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:589][962]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:608][963]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:608][963]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:638][964]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:638][964]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:678][965]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:678][965]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:692][966]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:692][966]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:720][967]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:720][967]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:756][968]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:756][968]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:789][969]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:789][969]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:805][970]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:805][970]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:833][971]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:833][971]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:876][972]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:876][972]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:890][973]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:890][973]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:918][974]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:918][974]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:947][975]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:947][975]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.29:980][976]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.29:980][976]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:011][977]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:011][977]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:029][978]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:029][978]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:057][979]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:057][979]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:086][980]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:086][980]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:113][981]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:113][981]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:144][982]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:144][982]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:169][983]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:169][983]LogTemp: Warning: Speed: 52.61, OnGround: true, Moving: true, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:169][983]LogTemp: Warning: STARTING MOVEMENT WINDOW
[2025.06.11-10.47.30:169][983]LogTemp: Warning: INCREMENTING frame count to: 1
[2025.06.11-10.47.30:197][984]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] true
[2025.06.11-10.47.30:197][984]LogTemp: Warning: Speed: 109.09, OnGround: true, Moving: true, WasMoving: true, InWindow: true, FrameCount: 1
[2025.06.11-10.47.30:197][984]LogTemp: Warning: INCREMENTING frame count to: 2
[2025.06.11-10.47.30:197][984]LogTemp: Warning: STOPPING - frame limit reached: 2 >= 2
[2025.06.11-10.47.30:225][985]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:225][985]LogTemp: Warning: Speed: 167.57, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:255][986]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:255][986]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:284][987]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:284][987]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:314][988]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:314][988]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:341][989]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:341][989]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:369][990]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:369][990]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:396][991]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:396][991]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:423][992]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:423][992]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:453][993]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:453][993]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:482][994]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:482][994]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:510][995]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:510][995]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:539][996]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:539][996]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:567][997]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:567][997]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:596][998]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:596][998]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:622][999]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:622][999]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:650][  0]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:650][  0]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:678][  1]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:678][  1]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:708][  2]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:708][  2]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:735][  3]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:735][  3]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:764][  4]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:764][  4]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:791][  5]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:791][  5]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:818][  6]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:818][  6]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:846][  7]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:846][  7]LogTemp: Warning: Speed: 200.00, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:873][  8]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:873][  8]LogTemp: Warning: Speed: 57.28, OnGround: true, Moving: true, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:900][  9]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:900][  9]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: true, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:930][ 10]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:930][ 10]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:958][ 11]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:958][ 11]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.30:988][ 12]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.30:989][ 12]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:015][ 13]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.31:015][ 13]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:042][ 14]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.31:042][ 14]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:069][ 15]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.31:069][ 15]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:096][ 16]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.31:096][ 16]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:124][ 17]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.31:124][ 17]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:154][ 18]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] false
[2025.06.11-10.47.31:154][ 18]LogTemp: Warning: Speed: 0.00, OnGround: true, Moving: false, WasMoving: false, InWindow: false, FrameCount: 0
[2025.06.11-10.47.31:191][ 18]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.47.31:191][ 18]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.11-10.47.31:194][ 18]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.11-10.47.31:194][ 18]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.11-10.47.31:195][ 18]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.11-10.47.31:197][ 18]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.47.31:210][ 18]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.11-10.47.31:222][ 18]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.11-10.47.31:264][ 18]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.11-10.47.31:264][ 18]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.11-10.47.31:264][ 18]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.11-10.47.31:265][ 18]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.11-10.47.31:270][ 18]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.11-10.47.31:296][ 18]LogUObjectHash: Compacting FUObjectHashTables data took   2.22ms
[2025.06.11-10.47.31:339][ 19]LogPlayLevel: Display: Destroying online subsystem :Context_24
[2025.06.11-10.48.23:638][ 10]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 341.662781
[2025.06.11-10.48.23:991][ 22]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.11-10.48.23:991][ 22]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 341.988647, Update Interval: 343.911865
